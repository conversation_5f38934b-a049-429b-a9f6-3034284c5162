import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Search, Eye, Trash2, Plus } from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { fetchWorkAreasByProjectId, loadDrafts, deleteWorkArea } from "@/store/slices/workAreasSlice";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";
import { WorkArea } from "@/types";

interface UnifiedOverlay {
  id: string | number;
  name: string;
  area_code: string;
  area_type: string;
  category: string;
  shapes: any[];
  isDraft: boolean; // Keep for compatibility
  isPending: boolean;
  updated_at: string;
  project_id: number;
  level_id: number;
}

interface UnifiedOverlayManagerProps {
  projectId: number;
}

const UnifiedOverlayManager = ({ projectId }: UnifiedOverlayManagerProps) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { workAreas } = useAppSelector((state) => state.workAreas);
  const [searchTerm, setSearchTerm] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [overlayToDelete, setOverlayToDelete] = useState<UnifiedOverlay | null>(null);

  // Load data on mount
  useEffect(() => {
    dispatch(fetchWorkAreasByProjectId(projectId));
    dispatch(loadDrafts());
  }, [dispatch, projectId]);

  // Get all overlays for this project and group by area_code to show only latest version
  const projectWorkAreas = workAreas.filter(wa => wa.project_id === projectId);

  // Group work areas by area_code (or area_name if no area_code)
  const groupedOverlays = projectWorkAreas.reduce((groups, wa) => {
    const key = wa.area_code || wa.area_name || `untitled_${wa.id}`;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(wa);
    return groups;
  }, {} as Record<string, WorkArea[]>);

  // For each group, select the latest version (prioritize published over draft, then by updated_at)
  const allOverlays: UnifiedOverlay[] = Object.values(groupedOverlays).map(group => {
    // Sort by: published first (pending=false), then by updated_at descending
    const sortedGroup = group.sort((a, b) => {
      // First priority: published over pending
      if (a.pending !== b.pending) {
        return a.pending ? 1 : -1; // published (pending=false) comes first
      }
      // Second priority: most recent updated_at
      const aTime = new Date(a.updated_at || a.created_at || 0).getTime();
      const bTime = new Date(b.updated_at || b.created_at || 0).getTime();
      return bTime - aTime;
    });

    const latestVersion = sortedGroup[0];
    return {
      id: latestVersion.id,
      name: latestVersion.area_name || 'Untitled Overlay',
      area_code: latestVersion.area_code || '',
      area_type: latestVersion.area_type,
      category: latestVersion.category,
      shapes: latestVersion.shapes || latestVersion.coordinates || [],
      isPending: latestVersion.pending,
      isDraft: latestVersion.pending, // Keep for compatibility
      updated_at: latestVersion.updated_at || latestVersion.created_at || new Date().toISOString(),
      project_id: latestVersion.project_id,
      level_id: latestVersion.level_id
    };
  });

  // Filter overlays based on search
  const filteredOverlays = allOverlays.filter(overlay =>
    overlay.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    overlay.area_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    overlay.area_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleView = (overlay: UnifiedOverlay) => {
    navigate(`/projects/${projectId}/work-areas/create?overlay=${overlay.id}&isDraft=${overlay.isDraft}`);
  };

  const handleCreateNew = () => {
    navigate(`/projects/${projectId}/work-areas/create`);
  };

  const handleDelete = (overlay: UnifiedOverlay) => {
    setOverlayToDelete(overlay);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (overlayToDelete) {
      try {
        await dispatch(deleteWorkArea(overlayToDelete.id)).unwrap();
        toast.success('Overlay deleted successfully');
        setShowDeleteDialog(false);
        setOverlayToDelete(null);
      } catch (error) {
        toast.error('Failed to delete overlay');
      }
    }
  };


  const formatLastUpdated = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return 'Unknown';
    }
  };


  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Overlay Management</h2>
          <p className="text-sm text-muted-foreground">
            Manage all overlays for this project
          </p>
        </div>
        <Button onClick={handleCreateNew} className="bg-connecta-dark-navy hover:bg-connecta-navy text-white">
          <Plus className="w-4 h-4 mr-2" />
          Create New Overlay
        </Button>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search overlays..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Code</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Shapes</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredOverlays.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex flex-col items-center">
                    <p className="text-muted-foreground mb-2">No overlays found</p>
                    <Button onClick={handleCreateNew} size="sm">
                      Create First Overlay
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredOverlays.map((overlay) => (
                <TableRow key={`overlay-${overlay.id}`}>
                  <TableCell className="font-medium">{overlay.name}</TableCell>
                  <TableCell>{overlay.area_code}</TableCell>
                  <TableCell>{overlay.area_type}</TableCell>
                  <TableCell>{overlay.category}</TableCell>
                  <TableCell>{overlay.shapes.length}</TableCell>
                  <TableCell>{formatLastUpdated(overlay.updated_at)}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleView(overlay)}
                        className="text-connecta-cyan hover:text-connecta-navy"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(overlay)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Overlay</DialogTitle>
          </DialogHeader>
          <p>Are you sure you want to delete "{overlayToDelete?.name}"? This action cannot be undone.</p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </div>
  );
};

export default UnifiedOverlayManager;