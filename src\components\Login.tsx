<<<<<<<
import { useState } from "react";

=======
import { useState, useEffect } from "react";

import { useNavigate, useLocation, Link } from "react-router-dom";

import { Button } from "@/components/ui/button";

import { Input } from "@/components/ui/input";

import { Label } from "@/components/ui/label";

import { Checkbox } from "@/components/ui/checkbox";

import { Alert, AlertDescription } from "@/components/ui/alert";

import { useAuth } from "@/hooks/useAuth";

import connectaIcon from "@/assets/connecta-logo-icon.png";

import connectaIllustration from "@/assets/connecta-illustration.png";

>>>>>>>


<<<<<<<
import { Button } from "@/components/ui/button";

=======
const Login = () => {

  const [email, setEmail] = useState("<EMAIL>");

  const [password, setPassword] = useState("password123");

  const [keepLoggedIn, setKeepLoggedIn] = useState(true);

>>>>>>>


<<<<<<<
import { Input } from "@/components/ui/input";

=======
  const { login, isAuthenticated, loading, error, clearAuthError, user } = useAuth();

  const navigate = useNavigate();

  const location = useLocation();



  // Redirect if already authenticated

  useEffect(() => {

    if (isAuthenticated && user) {

      const from = (location.state as any)?.from?.pathname || getDefaultRoute(user.user_type);

      navigate(from, { replace: true });

    }

  }, [isAuthenticated, user, navigate, location]);

>>>>>>>


<<<<<<<
import { Label } from "@/components/ui/label";

=======
  // Clear error when component mounts

  useEffect(() => {

    clearAuthError();

  }, [clearAuthError]);



  const getDefaultRoute = (userType: string) => {

    switch (userType) {

      case 'super_admin':

        return '/admin';

      case 'project_admin':

      case 'service_provider':

        return '/projects';

      case 'inspector':

        return '/projects';

      default:

        return '/projects';

    }

  };

>>>>>>>


<<<<<<<
import { Checkbox } from "@/components/ui/checkbox";

=======
  const handleSubmit = async (e: React.FormEvent) => {

    e.preventDefault();



    try {

      const result = await login(email, password);



      if (result.meta.requestStatus === 'fulfilled') {

        // Navigation will be handled by the useEffect above

      }

    } catch (err) {

      console.error('Login failed:', err);

    }

  };



  return (

    <div className="min-h-screen flex">

      {/* Left side - Illustration */}

      <div className="hidden lg:flex lg:w-1/2 bg-background items-center justify-center p-12">

        <div className="max-w-lg">

          {/* Logo */}

          <div className="flex items-center mb-12">

            <img 

              src={connectaIcon} 

              alt="ConnectaBuild" 

              className="w-12 h-12 mr-4"

            />

            <h1 className="text-4xl font-bold">

              <span className="text-connecta-navy">Connecta</span>

              <span className="text-connecta-cyan">Build</span>

            </h1>

          </div>

          

          {/* Illustration */}

          <div className="flex justify-center">

            <img 

              src={connectaIllustration} 

              alt="ConnectaBuild Platform" 

              className="w-full max-w-md"

            />

          </div>

        </div>

      </div>

>>>>>>>


import connectaIcon from "@/assets/connecta-logo-icon.png";



import connectaIllustration from "@/assets/connecta-illustration.png";



<<<<<<<


=======
          {/* Error Alert */}

          {error && (

            <Alert className="border-destructive">

              <AlertDescription className="text-destructive">

                {error}

              </AlertDescription>

            </Alert>

          )}



          {/* Demo Accounts Info */}

          <div className="bg-muted p-4 rounded-lg text-sm">

            <p className="font-medium mb-2">Demo Accounts:</p>

            <div className="space-y-1 text-muted-foreground">

              <p>• <EMAIL> (Super Admin)</p>

              <p>• <EMAIL> (Project Admin)</p>

              <p>• <EMAIL> (Service Provider)</p>

              <p>• <EMAIL> (Inspector)</p>

            </div>

          </div>



          {/* Login Form */}

          <form onSubmit={handleSubmit} className="space-y-6">

            <div className="space-y-2">

              <Label htmlFor="email" className="text-connecta-navy font-medium">

                Email Address

              </Label>

              <Input

                id="email"

                type="email"

                value={email}

                onChange={(e) => setEmail(e.target.value)}

                className="h-12 border-border focus:border-connecta-cyan focus:ring-connecta-cyan"

                placeholder="Enter your email"

                required

                disabled={loading}

              />

            </div>

>>>>>>>


<<<<<<<
const Login = () => {

=======
            <div className="space-y-2">

              <Label htmlFor="password" className="text-connecta-navy font-medium">

                Password

              </Label>

              <Input

                id="password"

                type="password"

                value={password}

                onChange={(e) => setPassword(e.target.value)}

                className="h-12 border-border focus:border-connecta-cyan focus:ring-connecta-cyan"

                placeholder="Enter your password"

                required

                disabled={loading}

              />

            </div>

>>>>>>>


<<<<<<<
  const [email, setEmail] = useState("<EMAIL>");

=======
            <div className="flex items-center justify-between">

              <div className="flex items-center space-x-2">

                <Checkbox

                  id="keep-logged-in"

                  checked={keepLoggedIn}

                  onCheckedChange={(checked) => setKeepLoggedIn(checked === true)}

                  className="border-border data-[state=checked]:bg-connecta-cyan data-[state=checked]:border-connecta-cyan"

                />

                <Label 

                  htmlFor="keep-logged-in" 

                  className="text-sm text-foreground font-normal cursor-pointer"

                >

                  Keep me logged in

                </Label>

              </div>

              

              <Link

                to="/forgot-password"

                className="text-sm text-connecta-cyan hover:underline font-medium"

              >

                Reset Password

              </Link>

            </div>

>>>>>>>


<<<<<<<
  const [password, setPassword] = useState("••••••••••••••••");

=======
            <Button

              type="submit"

              className="w-full h-12 bg-connecta-dark-navy hover:bg-connecta-navy text-white font-medium text-lg"

              disabled={loading}

            >

              {loading ? "Signing in..." : "Login"}

            </Button>

          </form>

        </div>

      </div>

    </div>

  );

};

>>>>>>>


  const [keepLoggedIn, setKeepLoggedIn] = useState(true);







  const handleSubmit = (e: React.FormEvent) => {



    e.preventDefault();



    



    // Mock authentication - determine role based on email domain or specific emails



    let userRole = "Guest";



    if (email.includes("superadmin")) {



      userRole = "Super Admin";



    } else if (email.includes("projectadmin")) {



      userRole = "Project Admin";



    } else if (email.includes("serviceprovider")) {



      userRole = "Service Provider";



    } else if (email.includes("inspector")) {



      userRole = "Inspector";



    }







    // Store user role and redirect based on role



    localStorage.setItem("userRole", userRole);



    localStorage.setItem("userEmail", email);



    



    // Redirect based on role



    if (userRole === "Super Admin") {



      window.location.href = "/admin";



    } else if (userRole === "Project Admin" || userRole === "Service Provider") {



      window.location.href = "/projects";



    } else {



      window.location.href = "/";



    }



  };







  return (



    <div className="min-h-screen flex">



      {/* Left side - Illustration */}



      <div className="hidden lg:flex lg:w-1/2 bg-background items-center justify-center p-12">



        <div className="max-w-lg">



          {/* Logo */}



          <div className="flex items-center mb-12">



            <img 



              src={connectaIcon} 



              alt="ConnectaBuild" 



              className="w-12 h-12 mr-4"



            />



            <h1 className="text-4xl font-bold">



              <span className="text-connecta-navy">Connecta</span>



              <span className="text-connecta-cyan">Build</span>



            </h1>



          </div>



          



          {/* Illustration */}



          <div className="flex justify-center">



            <img 



              src={connectaIllustration} 



              alt="ConnectaBuild Platform" 



              className="w-full max-w-md"



            />



          </div>



        </div>



      </div>







      {/* Right side - Login Form */}



      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-white">



        <div className="w-full max-w-md space-y-8">



          {/* Mobile Logo */}



          <div className="lg:hidden flex items-center justify-center mb-8">



            <img 



              src={connectaIcon} 



              alt="ConnectaBuild" 



              className="w-10 h-10 mr-3"



            />



            <h1 className="text-3xl font-bold">



              <span className="text-connecta-navy">Connecta</span>



              <span className="text-connecta-cyan">Build</span>



            </h1>



          </div>







          {/* Welcome Header */}



          <div className="text-center lg:text-left">



            <h2 className="text-3xl font-bold text-connecta-navy mb-2">Welcome Back</h2>



            <p className="text-muted-foreground">



              Are you new to ConnectaBuild?{" "}



              <a href="#" className="text-connecta-cyan hover:underline font-medium">



                Create An Account



              </a>



            </p>



          </div>







          {/* Login Form */}



          <form onSubmit={handleSubmit} className="space-y-6">



            <div className="space-y-2">



              <Label htmlFor="email" className="text-connecta-navy font-medium">



                Email Address



              </Label>



              <Input



                id="email"



                type="email"



                value={email}



                onChange={(e) => setEmail(e.target.value)}



                className="h-12 border-border focus:border-connecta-cyan focus:ring-connecta-cyan"



                placeholder="Enter your email"



              />



            </div>







            <div className="space-y-2">



              <Label htmlFor="password" className="text-connecta-navy font-medium">



                Password



              </Label>



              <Input



                id="password"



                type="password"



                value={password}



                onChange={(e) => setPassword(e.target.value)}



                className="h-12 border-border focus:border-connecta-cyan focus:ring-connecta-cyan"



                placeholder="Enter your password"



              />



            </div>







            <div className="flex items-center justify-between">



              <div className="flex items-center space-x-2">



                <Checkbox



                  id="keep-logged-in"



                  checked={keepLoggedIn}



                  onCheckedChange={(checked) => setKeepLoggedIn(checked === true)}



                  className="border-border data-[state=checked]:bg-connecta-cyan data-[state=checked]:border-connecta-cyan"



                />



                <Label 



                  htmlFor="keep-logged-in" 



                  className="text-sm text-foreground font-normal cursor-pointer"



                >



                  Keep me logged in



                </Label>



              </div>



              



              <a 



                href="#" 



                className="text-sm text-connecta-cyan hover:underline font-medium"



              >



                Reset Password



              </a>



            </div>







            <Button



              type="submit"



              className="w-full h-12 bg-connecta-dark-navy hover:bg-connecta-navy text-white font-medium text-lg"



            >



              Login



            </Button>



          </form>



        </div>



      </div>



    </div>



  );



};







export default Login;
