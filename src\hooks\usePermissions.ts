import { useSelector } from 'react-redux';
import { useCallback } from 'react';
import { RootState } from '@/store';
import { selectRolePermissions, selectUserPermissions, Permission } from '@/store/slices/permissionsSlice';
import { useAuth } from './useAuth';

export const usePermissions = () => {
  const { user } = useAuth();
  const permissions = useSelector((state: RootState) => state.permissions);

  // Get permissions for current user
  const getUserPermissions = useCallback((): Permission[] => {
    if (!user) return [];

    // Get role-based permissions
    const rolePermissions = selectRolePermissions({ permissions }, user.user_type);
    
    // Get user-specific permissions (overrides)
    const userPermissions = selectUserPermissions({ permissions }, user.id);
    
    // Combine permissions (user-specific overrides role-based)
    if (userPermissions.length > 0) {
      return userPermissions;
    }
    
    return rolePermissions;
  }, [user, permissions]);

  // Check if user has a specific permission
  const hasPermission = useCallback((permissionId: string): boolean => {
    const userPermissions = getUserPermissions();
    return userPermissions.some(p => p.id === permissionId);
  }, [getUserPermissions]);

  // Check if user has permission for a resource and action
  const hasResourcePermission = useCallback((resource: string, action: string): boolean => {
    const permissionId = `${resource}.${action}`;
    return hasPermission(permissionId);
  }, [hasPermission]);

  // Check multiple permissions (AND logic)
  const hasAllPermissions = useCallback((permissionIds: string[]): boolean => {
    return permissionIds.every(id => hasPermission(id));
  }, [hasPermission]);

  // Check multiple permissions (OR logic)
  const hasAnyPermission = useCallback((permissionIds: string[]): boolean => {
    return permissionIds.some(id => hasPermission(id));
  }, [hasPermission]);

  // Get permissions by resource
  const getResourcePermissions = useCallback((resource: string): Permission[] => {
    const userPermissions = getUserPermissions();
    return userPermissions.filter(p => p.resource === resource);
  }, [getUserPermissions]);

  // Common permission checks
  const canCreateUsers = useCallback(() => hasPermission('users.create'), [hasPermission]);
  const canUpdateUsers = useCallback(() => hasPermission('users.update'), [hasPermission]);
  const canDeleteUsers = useCallback(() => hasPermission('users.delete'), [hasPermission]);
  const canAssignRoles = useCallback(() => hasPermission('users.assign_roles'), [hasPermission]);
  
  const canCreateProjects = useCallback(() => hasPermission('projects.create'), [hasPermission]);
  const canUpdateProjects = useCallback(() => hasPermission('projects.update'), [hasPermission]);
  const canDeleteProjects = useCallback(() => hasPermission('projects.delete'), [hasPermission]);
  const canManageProjectTeam = useCallback(() => hasPermission('projects.manage_team'), [hasPermission]);
  
  const canUploadPlans = useCallback(() => hasPermission('plans.upload'), [hasPermission]);
  const canUpdatePlans = useCallback(() => hasPermission('plans.update'), [hasPermission]);
  const canDeletePlans = useCallback(() => hasPermission('plans.delete'), [hasPermission]);
  
  const canCreateWorkAreas = useCallback(() => hasPermission('work_areas.create'), [hasPermission]);
  const canUpdateWorkAreas = useCallback(() => hasPermission('work_areas.update'), [hasPermission]);
  const canDeleteWorkAreas = useCallback(() => hasPermission('work_areas.delete'), [hasPermission]);
  
  const canCreatePenetrations = useCallback(() => hasPermission('penetrations.create'), [hasPermission]);
  const canUpdatePenetrations = useCallback(() => hasPermission('penetrations.update'), [hasPermission]);
  const canInspectPenetrations = useCallback(() => hasPermission('penetrations.inspect'), [hasPermission]);
  const canApprovePenetrations = useCallback(() => hasPermission('penetrations.approve'), [hasPermission]);
  
  const canAccessAdmin = useCallback(() => hasPermission('admin.access'), [hasPermission]);
  const canManageSystemSettings = useCallback(() => hasPermission('admin.system_settings'), [hasPermission]);
  
  const canGenerateReports = useCallback(() => hasPermission('reports.generate'), [hasPermission]);
  const canExportReports = useCallback(() => hasPermission('reports.export'), [hasPermission]);

  return {
    // Core permission functions
    getUserPermissions,
    hasPermission,
    hasResourcePermission,
    hasAllPermissions,
    hasAnyPermission,
    getResourcePermissions,

    // User management permissions
    canCreateUsers,
    canUpdateUsers,
    canDeleteUsers,
    canAssignRoles,

    // Project management permissions
    canCreateProjects,
    canUpdateProjects,
    canDeleteProjects,
    canManageProjectTeam,

    // Plans & overlays permissions
    canUploadPlans,
    canUpdatePlans,
    canDeletePlans,

    // Work areas permissions
    canCreateWorkAreas,
    canUpdateWorkAreas,
    canDeleteWorkAreas,

    // Penetrations permissions
    canCreatePenetrations,
    canUpdatePenetrations,
    canInspectPenetrations,
    canApprovePenetrations,

    // Admin permissions
    canAccessAdmin,
    canManageSystemSettings,

    // Reports permissions
    canGenerateReports,
    canExportReports,
  };
};
