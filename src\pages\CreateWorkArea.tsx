import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>ef<PERSON>, <PERSON>, Copy, Plus, Trash2, X, Upload, Undo2, ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/hooks";
import {
  fetchWorkAreasByLevelId,
  fetchWorkAreasByProjectId,
  createWorkArea,
  updateWorkArea,
  loadDrafts,
  createDraft,
  setCurrentWorkArea,
  updateCurrentWorkArea,
  addShapeToCurrentWorkArea,
  removeShapeFromCurrentWorkArea,
  saveDraft
} from "@/store/slices/workAreasSlice";
import { setCurrentProject } from "@/store/slices/projectsSlice";
import { fetchPenetrationsByProjectId } from "@/store/slices/firePenetrationsSlice";
import * as pdfjsLib from 'pdfjs-dist';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { toast } from "sonner";
import { WorkAreaShape, WorkArea } from "@/types";

// Set PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

type WorkAreaType = "Floor" | "Wall" | "Ceiling";

interface Tag {
  id: string;
  name: string;
}

interface Point {
  x: number;
  y: number;
}

interface DrawingSet {
  id: string;
  name: string;
  shapes: WorkAreaShape[];
  created: Date;
}

interface HistoryState {
  shapes: WorkAreaShape[];
  timestamp: number;
}

// Default plan image - a simple floor plan placeholder
const DEFAULT_PLAN_IMAGE = "/lovable-uploads/43668952-d2e0-4dc7-a431-1d07e6e2be82.png";

const CreateWorkArea = () => {
  const navigate = useNavigate();
  const { projectId } = useParams<{ projectId: string }>();
  const [searchParams] = useSearchParams();
  const dispatch = useAppDispatch();
  const { workAreas, currentWorkArea, loading } = useAppSelector((state) => state.workAreas);
  const { penetrations } = useAppSelector((state) => state.firePenetrations);

  // Get current pending area (work area with pending=true)
  const currentPending = currentWorkArea;
  const pendingAreas = workAreas.filter(wa => wa.pending);

  // Load data on mount and set current project
  useEffect(() => {
    if (projectId) {
      dispatch(fetchWorkAreasByProjectId(parseInt(projectId))); // Load all work areas for project
      dispatch(fetchPenetrationsByProjectId(parseInt(projectId)));
      dispatch(loadDrafts()); // Load pending areas from localStorage

      // Set current project in store
      dispatch(setCurrentProject({ id: parseInt(projectId) }));
    }
  }, [dispatch, projectId]);

  // Auto-save functionality - save pending areas on ANY changes (including drawing)

  // Handle URL parameters for loading specific overlays
  useEffect(() => {
    if (projectId && workAreas.length > 0) {
      const overlayId = searchParams.get('overlay');
      const isPendingParam = searchParams.get('isDraft') === 'true'; // Keep param name for compatibility

      if (overlayId) {
        // Load specific overlay from URL params
        const targetOverlay = workAreas.find(wa =>
          wa.id.toString() === overlayId && wa.pending === isPendingParam
        );

        if (targetOverlay) {
          dispatch(setCurrentWorkArea(targetOverlay));
          // Update level selector to match the loaded overlay
          const levelName = Object.keys(levelMapping).find(
            key => levelMapping[key as keyof typeof levelMapping] === targetOverlay.level_id
          );
          if (levelName) {
            setSelectedLevel(levelName);
          }
          return;
        }
      }
    }
  }, [projectId, workAreas, searchParams, dispatch]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedWorkAreaType, setSelectedWorkAreaType] = useState<WorkAreaType>("Floor");
  const [selectedLevel, setSelectedLevel] = useState("Level 1");

  // Map level names to level_id numbers
  const levelMapping = {
    "Level 1": 1,
    "Level 2": 2,
    "Level 3": 3,
    "Level 4": 4,
    "Level 5": 5
  } as const;

  const currentLevelId = levelMapping[selectedLevel as keyof typeof levelMapping] || 1;

  // Handle level changes - load overlay for selected level
  useEffect(() => {
    if (projectId && currentLevelId) {
      // Skip if URL params are being processed
      const overlayId = searchParams.get('overlay');
      if (overlayId) return;

      // Check if we already have the right overlay loaded
      if (currentPending && currentPending.level_id === currentLevelId) {
        return; // Already have the correct overlay loaded
      }

      // Find existing overlay for this level (prioritize published over pending)
      const levelOverlays = workAreas.filter(wa =>
        wa.project_id === parseInt(projectId) && wa.level_id === currentLevelId
      );

      console.log(`Level switching to ${selectedLevel} (ID: ${currentLevelId}):`, {
        totalWorkAreas: workAreas.length,
        levelOverlays: levelOverlays.length,
        levelOverlaysData: levelOverlays.map(wa => ({
          id: wa.id,
          level_id: wa.level_id,
          pending: wa.pending,
          shapesCount: wa.shapes?.length || 0
        }))
      });

      if (levelOverlays.length > 0) {
        // Sort to get the best overlay (published first, then most recent)
        const sortedOverlays = levelOverlays.sort((a, b) => {
          if (a.pending !== b.pending) return a.pending ? 1 : -1;
          const aTime = new Date(a.updated_at || a.created_at || 0).getTime();
          const bTime = new Date(b.updated_at || b.created_at || 0).getTime();
          return bTime - aTime;
        });

        const bestOverlay = sortedOverlays[0];
        console.log(`Loading existing overlay for ${selectedLevel}:`, {
          id: bestOverlay.id,
          pending: bestOverlay.pending,
          shapesCount: bestOverlay.shapes?.length || 0
        });
        dispatch(setCurrentWorkArea(bestOverlay));
      } else if (workAreas.length > 0) {
        // Only create new pending area if we have loaded work areas (to avoid creating on initial load)
        console.log(`Creating new pending area for ${selectedLevel}`);
        dispatch(createDraft({
          projectId: parseInt(projectId),
          levelId: currentLevelId
        }));
      }
    }
  }, [selectedLevel, currentLevelId, projectId, currentPending?.level_id, workAreas.length, searchParams, dispatch]);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [tags, setTags] = useState<Tag[]>([]);
  const [newTag, setNewTag] = useState("");
  const [substrateMaterial, setSubstrateMaterial] = useState("");
  const [frlColor, setFrlColor] = useState("");
  const [frlValues, setFrlValues] = useState({ first: 0, second: 0, third: 0 });
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawingMode, setDrawingMode] = useState<WorkAreaType | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showCreateDropdown, setShowCreateDropdown] = useState(false);
  
  // PDF-related state
  const [pdfImages, setPdfImages] = useState<string[]>([]);
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPageIndex, setCurrentPageIndex] = useState<number>(0);
  const [scale, setScale] = useState<number>(1.0);
  const [pdfError, setPdfError] = useState<string | null>(null);
  const [isLoadingPdf, setIsLoadingPdf] = useState<boolean>(false);
  const [useDefaultPlan, setUseDefaultPlan] = useState<boolean>(true);

  // Drawing state - now uses currentPending.shapes
  const shapes = currentPending?.shapes || [];
  console.log('Current shapes in render:', { 
    currentPendingId: currentPending?.id,
    shapesCount: shapes.length, 
    shapes: shapes.map(s => ({ id: s.id, type: s.type, pointsCount: s.points?.length }))
  });
  const [currentShape, setCurrentShape] = useState<Point[]>([]);
  const [isDrawingShape, setIsDrawingShape] = useState(false);
  
  // Multiple drawings state
  const [drawingSets, setDrawingSets] = useState<DrawingSet[]>([]);
  const [activeDrawingSet, setActiveDrawingSet] = useState<string>("");
  const [isEditingPoints, setIsEditingPoints] = useState(false);
  const [selectedShape, setSelectedShape] = useState<string | null>(null);
  const [draggedPoint, setDraggedPoint] = useState<{ shapeId: string; pointIndex: number } | null>(null);
  const [selectedPoint, setSelectedPoint] = useState<{ shapeId: string; pointIndex: number } | null>(null);
  
  // Scroll-based navigation state
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isMiddleMouseDragging, setIsMiddleMouseDragging] = useState(false);
  const [lastMousePosition, setLastMousePosition] = useState({ x: 0, y: 0 });
  
  // History for undo functionality
  const [history, setHistory] = useState<HistoryState[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Sync form fields with current pending area
  useEffect(() => {
    if (currentPending) {
      console.log('Current pending updated with shapes:', currentPending.shapes?.length || 0);
      setSelectedCategory(currentPending.category);
      setSubstrateMaterial(currentPending.substrate_material || '');
      setFrlColor(currentPending.color || '');
      
      // Convert pending tags to local format
      const pendingTags = currentPending.tags?.map((tag, index) => ({
        id: `tag_${index}`,
        name: tag
      })) || [];
      setTags(pendingTags);
    }
  }, [currentPending]);

  // Auto-update pending area when form fields change
  const updatePendingField = (field: keyof WorkArea, value: any) => {
    if (currentPending) {
      dispatch(updateCurrentWorkArea({ [field]: value }));
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.find(tag => tag.name === newTag.trim())) {
      const newTagObj = { id: Date.now().toString(), name: newTag.trim() };
      const updatedTags = [...tags, newTagObj];
      setTags(updatedTags);
      
      // Update draft
      const updatedTagNames = updatedTags.map(t => t.name);
      dispatch(updateCurrentWorkArea({ tags: updatedTagNames }));
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagId: string) => {
    const tagToRemove = tags.find(tag => tag.id === tagId);
    if (tagToRemove) {
      const updatedTags = tags.filter(tag => tag.id !== tagId);
      setTags(updatedTags);
      const updatedTagNames = updatedTags.map(t => t.name);
      dispatch(updateCurrentWorkArea({ tags: updatedTagNames }));
    }
  };

  const handleCreateNew = (type: WorkAreaType) => {
    setDrawingMode(type);
    setIsDrawing(true);
    setIsDrawingShape(false);
    setCurrentShape([]);
    setShowCreateDropdown(false);
    toast(`Creating a ${type.toLowerCase()}. Click anywhere on the map to start drawing.`);
  };

  const handleCancelDrawing = () => {
    setIsDrawing(false);
    setDrawingMode(null);
    setIsDrawingShape(false);
    setCurrentShape([]);
    toast("Drawing cancelled");
  };

  const getShapeColor = (type: WorkAreaType) => {
    switch (type) {
      case "Wall": return "#ef4444"; // red
      case "Floor": return "#3b82f6"; // blue  
      case "Ceiling": return "#10b981"; // green
      default: return "#6b7280"; // gray
    }
  };

  const handleCanvasClick = (e: React.MouseEvent) => {
    // Don't allow drawing when in edit points mode
    if (isEditingPoints || !isDrawing || !drawingMode) return;

    // Get the image element to calculate proper coordinates
    const imageElement = e.currentTarget.querySelector('img');
    if (!imageElement) return;

    const imageRect = imageElement.getBoundingClientRect();
    
    // Calculate click position with maximum precision
    // Account for any CSS transforms, borders, and ensure pixel-perfect accuracy
    const svgViewBoxWidth = 2400;
    const svgViewBoxHeight = 1800;
    
    // Get exact position within the image bounds
    const relativeX = (e.clientX - imageRect.left) / imageRect.width;
    const relativeY = (e.clientY - imageRect.top) / imageRect.height;
    
    // Clamp to ensure we're within bounds
    const clampedX = Math.max(0, Math.min(1, relativeX));
    const clampedY = Math.max(0, Math.min(1, relativeY));
    
    const x = clampedX * svgViewBoxWidth;
    const y = clampedY * svgViewBoxHeight;

    console.log('Click coordinates:', { 
      x: Math.round(x), y: Math.round(y), 
      relativeX: clampedX, relativeY: clampedY,
      clientX: e.clientX, clientY: e.clientY,
      imageRect: { left: imageRect.left, top: imageRect.top, width: imageRect.width, height: imageRect.height }
    });

    if (!isDrawingShape) {
      // Start drawing
      setIsDrawingShape(true);
      setCurrentShape([{ x: Math.round(x), y: Math.round(y) }]);
      toast(`Started drawing ${drawingMode}. Click to add points, double-click to finish.`);
    } else {
      // Add point to current shape
      const newPoint = { x: Math.round(x), y: Math.round(y) };
      const updatedShape = [...currentShape, newPoint];
      
      // Check if we're close to the starting point (to close the shape) - scale the threshold with resolution
      const startPoint = currentShape[0];
      const distance = Math.sqrt(Math.pow(x - startPoint.x, 2) + Math.pow(y - startPoint.y, 2));
      
      if (distance < 80 && currentShape.length >= 3) { // Increased threshold for higher resolution
        // Close the shape
        finishShape();
      } else {
        setCurrentShape(updatedShape);
      }
    }
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (isDrawingShape && currentShape.length >= 2) {
      finishShape();
    }
  };

  const addToHistory = (newShapes: WorkAreaShape[]) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push({ shapes: [...newShapes], timestamp: Date.now() });
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  const finishShape = () => {
    if (currentShape.length >= 2 && drawingMode && currentPending) {
      const newShape: WorkAreaShape = {
        id: Date.now().toString(),
        type: drawingMode,
        points: [...currentShape],
        completed: true,
        color: getShapeColor(drawingMode),
        name: `${drawingMode} ${shapes.length + 1}`
      };
      
      addToHistory(shapes); // Save current state before change
      dispatch(addShapeToCurrentWorkArea(newShape));
      
      // Auto-save will trigger due to state change
      console.log('Shape added to draft:', newShape);
      
      setCurrentShape([]);
      setIsDrawingShape(false);
      setIsDrawing(false);
      setDrawingMode(null);
      
      // Console recap of all items
      logDrawingRecap([...shapes, newShape]);
      
      toast(`${drawingMode} saved to draft. Click "Save and Exit" to create work area.`);
    }
  };

  const handleUndo = () => {
    if (historyIndex > 0) {
      const previousState = history[historyIndex - 1];
      setHistoryIndex(historyIndex - 1);
      toast("Undo successful");
    } else {
      toast("Nothing to undo");
    }
  };

  const updateStorage = (currentShapes: WorkAreaShape[]) => {
    if (!currentPending) return;
    
    const drawingData = {
    };
    
    localStorage.setItem('workAreaDrawings', JSON.stringify(drawingData));
    console.log('Storage updated with:', drawingData);
  };

  const createNewDrawingSet = () => {
    const newSet: DrawingSet = {
      id: Date.now().toString(),
      name: `Drawing Set ${drawingSets.length + 1}`,
      shapes: [...shapes],
      created: new Date()
    };
    
    setDrawingSets(prev => [...prev, newSet]);
    setActiveDrawingSet(newSet.id);
    
    // Clear current shapes and start fresh
    setHistory([]);
    setHistoryIndex(-1);
    
    toast("New drawing set created");
  };

  const switchToDrawingSet = (setId: string) => {
    // Load selected set
    const selectedSet = drawingSets.find(set => set.id === setId);
    if (selectedSet) {
      // Update current draft with selected shapes
      dispatch(updateCurrentWorkArea({ shapes: selectedSet.shapes }));
      setActiveDrawingSet(setId);
    }
  };

  const handlePointClick = (e: React.MouseEvent, shapeId: string, pointIndex: number) => {
    e.stopPropagation();
    if (!isEditingPoints) return;
    
    setSelectedPoint({ shapeId, pointIndex });
  };

  const handlePointMouseDown = (e: React.MouseEvent, shapeId: string, pointIndex: number) => {
    e.stopPropagation();
    if (!isEditingPoints) return;
    
    // Save the original position for potential undo
    addToHistory(shapes);
    setDraggedPoint({ shapeId, pointIndex });
    setSelectedPoint({ shapeId, pointIndex });
  };

  const deleteSelectedPoint = () => {
    if (!selectedPoint) return;
    
    const shape = shapes.find(s => s.id === selectedPoint.shapeId);
    if (!shape || shape.points.length <= 3) {
      toast("Cannot delete point - minimum 3 points required");
      return;
    }
    
    addToHistory(shapes);
    const updatedShapes = shapes.map(s => {
      if (s.id === selectedPoint.shapeId) {
        const newPoints = s.points.filter((_, index) => index !== selectedPoint.pointIndex);
        return { ...s, points: newPoints };
      }
      return s;
    });
    
    // Update pending area with new shapes
    if (currentPending) {
      dispatch(updateCurrentWorkArea({ shapes: updatedShapes }));
    }
    setSelectedPoint(null);
    toast("Point deleted");
  };

  const deleteShape = (shapeId: string) => {
    addToHistory(shapes);
    const updatedShapes = shapes.filter(shape => shape.id !== shapeId);
    // Update pending area
    if (currentPending) {
      dispatch(updateCurrentWorkArea({ shapes: updatedShapes }));
    }
    toast("Shape deleted");
  };

  const logDrawingRecap = (currentShapes: WorkAreaShape[]) => {
    console.log("=== DRAWING RECAP ===");
    console.log(`Total shapes: ${currentShapes.length}`);
    console.log(`Level: ${selectedLevel}`);
    console.log(`Work Area Name: ${currentPending?.area_name || 'Untitled'}`);
    console.log(`Category: ${selectedCategory}`);
    console.log("Shapes breakdown:");
    
    const shapesByType = currentShapes.reduce((acc, shape) => {
      acc[shape.type] = (acc[shape.type] || 0) + 1;
      return acc;
    }, {} as Record<WorkAreaType, number>);
    
    Object.entries(shapesByType).forEach(([type, count]) => {
      console.log(`  ${type}: ${count} items`);
    });
    
    console.log("Detailed shape list:");
    currentShapes.forEach((shape, index) => {
      console.log(`  ${index + 1}. ${shape.name} (${shape.type}) - ${shape.points.length} points`);
    });
    console.log("==================");
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    console.log('Mouse down - button:', e.button, 'scale:', scale);
    // Handle middle mouse button for panning when zoomed in
    if (e.button === 1) { // Middle mouse button
      console.log('Middle mouse button detected, scale:', scale);
      e.preventDefault();
      setIsMiddleMouseDragging(true);
      setLastMousePosition({ x: e.clientX, y: e.clientY });
      console.log('Started middle mouse dragging');
    }
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    if (e.button === 1) { // Middle mouse button
      setIsMiddleMouseDragging(false);
    }
    
    // Commit point position on mouse release
    if (draggedPoint && isEditingPoints) {
      setDraggedPoint(null);
      toast("Point position updated");
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    // Handle middle mouse button dragging for panning
    if (isMiddleMouseDragging) {
      console.log('Middle mouse dragging active, scale:', scale);
      e.preventDefault();
      const deltaX = e.clientX - lastMousePosition.x;
      const deltaY = e.clientY - lastMousePosition.y;
      
      setPanOffset(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));
      
      setLastMousePosition({ x: e.clientX, y: e.clientY });
      console.log('Pan offset updated:', { deltaX, deltaY });
      return;
    }
    // Handle point dragging with precise coordinates
    if (draggedPoint && isEditingPoints) {
      const imageElement = e.currentTarget.querySelector('img');
      if (!imageElement) return;

      const imageRect = imageElement.getBoundingClientRect();
      
      // Use the same precise coordinate calculation as click handler
      const svgViewBoxWidth = 2400;
      const svgViewBoxHeight = 1800;
      
      const relativeX = (e.clientX - imageRect.left) / imageRect.width;
      const relativeY = (e.clientY - imageRect.top) / imageRect.height;
      
      // Clamp to ensure we're within bounds
      const clampedX = Math.max(0, Math.min(1, relativeX));
      const clampedY = Math.max(0, Math.min(1, relativeY));
      
      const x = Math.round(clampedX * svgViewBoxWidth);
      const y = Math.round(clampedY * svgViewBoxHeight);

      console.log('Dragging point to:', { x, y, relativeX: clampedX, relativeY: clampedY });

      const updatedShapes = shapes.map(shape => {
        if (shape.id === draggedPoint.shapeId) {
          const newPoints = [...shape.points];
          newPoints[draggedPoint.pointIndex] = { x, y };
          return { ...shape, points: newPoints };
        }
        return shape;
      });

      // Update pending area with new shapes
      if (currentPending) {
        dispatch(updateCurrentWorkArea({ shapes: updatedShapes }));
      }
    }
  };

  const resetPan = () => {
    setPanOffset({ x: 0, y: 0 });
    toast("Pan reset to center");
  };

  const cancelCurrentAction = () => {
    if (isDrawing) {
      handleCancelDrawing();
    } else if (selectedPoint) {
      setSelectedPoint(null);
    } else if (isEditingPoints) {
      setIsEditingPoints(false);
    }
  };

  // Handle ESC key to cancel actions, Delete key for points, and Ctrl+scroll zoom
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        cancelCurrentAction();
      } else if (e.key === 'Delete' && selectedPoint) {
        deleteSelectedPoint();
      }
    };

    const handleWheel = (e: WheelEvent) => {
      // Check if the mouse is over the canvas area
      const canvasElement = document.querySelector('.canvas-container');
      if (canvasElement) {
        const rect = canvasElement.getBoundingClientRect();
        const isOverCanvas = e.clientX >= rect.left && e.clientX <= rect.right && 
                            e.clientY >= rect.top && e.clientY <= rect.bottom;
        
        if (isOverCanvas) {
          if (e.ctrlKey) {
            // Zoom with Ctrl+scroll
            e.preventDefault();
            const zoomDelta = e.deltaY > 0 ? -0.1 : 0.1;
            setScale(prev => Math.max(0.5, Math.min(3.0, prev + zoomDelta)));
          } else if (scale > 1.0) {
            // Pan with scroll when zoomed in
            e.preventDefault();
            const panSpeed = 30;
            
            if (e.shiftKey) {
              // Horizontal scroll with Shift+scroll
              setPanOffset(prev => ({
                x: prev.x - (e.deltaY > 0 ? panSpeed : -panSpeed),
                y: prev.y
              }));
            } else {
              // Vertical scroll
              setPanOffset(prev => ({
                x: prev.x,
                y: prev.y - (e.deltaY > 0 ? panSpeed : -panSpeed)
              }));
            }
          }
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('wheel', handleWheel, { passive: false });
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('wheel', handleWheel);
    };
  }, [isDrawing, selectedPoint]);

  const handleDeleteWorkArea = () => {
    setShowDeleteDialog(false);
    toast("Work area deleted");
    // Reset current pending area
    if (currentPending) {
      dispatch(updateCurrentWorkArea({
        area_name: "",
        category: "apartment",
        substrate_material: "",
        color: "",
        tags: []
      }));
    }
    setTags([]);
    setSubstrateMaterial("");
    setFrlColor("");
    setFrlValues({ first: 0, second: 0, third: 0 });
  };

  const handleSaveAndExit = async () => {
    if (!projectId) {
      toast.error('No project ID available');
      return;
    }

    console.log('🔥 Starting save process for all levels with drawings...');

    try {
      // Save the current pending area if we're in drawing mode
      if (currentPending) {
        // Auto-save the pending area
      }

      // Get all work areas for this project (including drafts)
      const allProjectWorkAreas = workAreas.filter(wa =>
        wa.project_id === parseInt(projectId)
      );

      console.log('🔥 Found work areas for project:', {
        totalWorkAreas: allProjectWorkAreas.length,
        workAreas: allProjectWorkAreas.map(wa => ({
          id: wa.id,
          level_id: wa.level_id,
          pending: wa.pending,
          shapesCount: wa.shapes?.length || 0
        }))
      });

      // Group by level_id and find work areas with shapes
      const levelGroups: { [key: number]: typeof allProjectWorkAreas } = {};
      allProjectWorkAreas.forEach(wa => {
        if (!levelGroups[wa.level_id]) {
          levelGroups[wa.level_id] = [];
        }
        levelGroups[wa.level_id].push(wa);
      });

      let savedCount = 0;
      let totalShapes = 0;

      // Process each level
      for (const [levelId, levelWorkAreas] of Object.entries(levelGroups)) {
        const levelIdNum = parseInt(levelId);

        // Find the best work area for this level (draft with shapes, or published)
        const workAreasWithShapes = levelWorkAreas.filter(wa =>
          wa.shapes && wa.shapes.length > 0
        );

        if (workAreasWithShapes.length > 0) {
          // Sort to get the best work area (draft first if it has shapes, then most recent)
          const sortedWorkAreas = workAreasWithShapes.sort((a, b) => {
            if (a.pending !== b.pending) return a.pending ? -1 : 1; // Pending first
            const aTime = new Date(a.updated_at || a.created_at || 0).getTime();
            const bTime = new Date(b.updated_at || b.created_at || 0).getTime();
            return bTime - aTime;
          });

          const workAreaToSave = sortedWorkAreas[0];
          const shapesCount = workAreaToSave.shapes?.length || 0;

          console.log(`🔥 Saving Level ${levelIdNum} with ${shapesCount} shapes:`, {
            workAreaId: workAreaToSave.id,
            isPending: workAreaToSave.pending,
            shapesCount
          });

          // Create published work area
          const workAreaData = {
            project_id: parseInt(projectId),
            level_id: levelIdNum,
            area_code: workAreaToSave.area_code,
            area_name: workAreaToSave.area_name || `Overlay Level ${levelIdNum}`,
            area_type: workAreaToSave.area_type,
            category: workAreaToSave.category,
            frl_rating: workAreaToSave.frl_rating,
            color: workAreaToSave.color,
            substrate_type: workAreaToSave.substrate_type,
            substrate_material: workAreaToSave.substrate_material,
            tags: workAreaToSave.tags,
            shapes: workAreaToSave.shapes || [],
            pending: false // Mark as published
          };

          await dispatch(createWorkArea(workAreaData)).unwrap();
          savedCount++;
          totalShapes += shapesCount;

          console.log(`🔥 Level ${levelIdNum} saved successfully`);
        } else {
          console.log(`🔥 Level ${levelIdNum} has no shapes, skipping`);
        }
      }

      // Show success message
      if (savedCount > 0) {
        toast.success(`Successfully saved ${savedCount} level${savedCount > 1 ? 's' : ''} with ${totalShapes} total shapes!`);
      } else {
        toast.info('No overlays with drawings found to save');
      }

      console.log('🔥 Save process completed:', { savedCount, totalShapes });

      // Navigate back to projects
      setTimeout(() => {
        navigate("/projects");
      }, 500);

    } catch (error) {
      console.error('🔥 Save error:', error);
      toast.error('Failed to save overlays');
    }
  };

  // PDF handlers
  const convertPdfToImages = async (file: File) => {
    setIsLoadingPdf(true);
    setPdfError(null);
    
    try {
      console.log('Starting PDF conversion for file:', file.name, 'Size:', file.size);
      
      const arrayBuffer = await file.arrayBuffer();
      console.log('ArrayBuffer created, size:', arrayBuffer.byteLength);
      
      const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
      console.log('PDF loading task created');
      
      const pdf = await loadingTask.promise;
      console.log('PDF loaded successfully, pages:', pdf.numPages);
      
      const images: string[] = [];
      setNumPages(pdf.numPages);
      
      for (let pageNumber = 1; pageNumber <= pdf.numPages; pageNumber++) {
        console.log(`Converting page ${pageNumber} of ${pdf.numPages}`);
        
        const page = await pdf.getPage(pageNumber);
        const viewport = page.getViewport({ scale: 2.0 });
        
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        
        console.log(`Canvas created for page ${pageNumber}, size: ${canvas.width}x${canvas.height}`);
        
        if (context) {
          await page.render({
            canvasContext: context,
            viewport: viewport
          }).promise;
          
          const imageData = canvas.toDataURL('image/png');
          images.push(imageData);
          console.log(`Page ${pageNumber} rendered successfully`);
        } else {
          throw new Error(`Failed to get canvas context for page ${pageNumber}`);
        }
      }
      
      setPdfImages(images);
      setCurrentPageIndex(0);
      setUseDefaultPlan(false);
      setIsLoadingPdf(false);
      console.log('PDF conversion completed successfully');
      toast("PDF converted to images successfully!");
      
    } catch (error) {
      console.error('Detailed PDF conversion error:', error);
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
      
      let errorMessage = "Failed to convert PDF to images";
      if (error.name === 'InvalidPDFException') {
        errorMessage = "Invalid PDF file. Please check the file format.";
      } else if (error.name === 'MissingPDFException') {
        errorMessage = "PDF file appears to be corrupted or missing data.";
      } else if (error.name === 'UnexpectedResponseException') {
        errorMessage = "Unable to process PDF. The file might be password protected or corrupted.";
      }
      
      setPdfError(errorMessage);
      setIsLoadingPdf(false);
      toast.error(errorMessage);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      await convertPdfToImages(file);
    } else {
      toast.error("Please select a valid PDF file");
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleZoomIn = () => setScale(prev => Math.min(prev + 0.2, 3.0));
  const handleZoomOut = () => setScale(prev => Math.max(prev - 0.2, 0.5));
  const handleResetZoom = () => setScale(1.0);

  const goToPrevPage = () => setCurrentPageIndex(prev => Math.max(prev - 1, 0));
  const goToNextPage = () => setCurrentPageIndex(prev => Math.min(prev + 1, numPages - 1));

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-background px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => navigate("/projects")}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <div className="text-sm text-muted-foreground mb-1">
                Projects / Dee Why Suites
              </div>
              <h1 className="text-2xl font-semibold">Overlay Editor</h1>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
              <div className="text-xs text-muted-foreground">
                Pending: {shapes.length} polygon{shapes.length !== 1 ? 's' : ''}
              </div>
              </span>
            </div>
            <Button onClick={handleSaveAndExit} className="bg-black text-white hover:bg-black/90">
              Save All Levels & Exit
            </Button>
          </div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Left side - Drawing area */}
        <div className="flex-1 bg-gray-50 p-6">
          <div className="mb-4 flex items-center justify-between">
            <Select value={selectedLevel} onValueChange={setSelectedLevel}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Level 1">Level 1</SelectItem>
                <SelectItem value="Level 2">Level 2</SelectItem>
                <SelectItem value="Level 3">Level 3</SelectItem>
                <SelectItem value="Level 4">Level 4</SelectItem>
                <SelectItem value="Level 5">Level 5</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2">
              <Button 
                variant={isEditingPoints ? "default" : "outline"} 
                size="sm"
                onClick={() => setIsEditingPoints(!isEditingPoints)}
              >
                <Eye className="h-4 w-4 mr-2" />
                {isEditingPoints ? "Exit Edit" : "Edit Points"}
              </Button>
              <Button variant="outline" size="sm" onClick={handleUndo}>
                <Undo2 className="h-4 w-4 mr-2" />
                Undo
              </Button>
              <Button variant="outline" size="sm" onClick={createNewDrawingSet}>
                <Plus className="h-4 w-4 mr-2" />
                New Drawing Set
              </Button>
              <DropdownMenu open={showCreateDropdown} onOpenChange={setShowCreateDropdown}>
                <DropdownMenuTrigger asChild>
                  <Button size="sm" className="bg-black text-white hover:bg-black/90">
                    <Plus className="h-4 w-4 mr-2" />
                    Create New
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleCreateNew("Wall")}>
                    Wall
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleCreateNew("Floor")}>
                    Floor
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleCreateNew("Ceiling")}>
                    Ceiling
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Drawing canvas area */}
          <div className="bg-white border rounded-lg h-[calc(100%-80px)] relative overflow-hidden">
            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept=".pdf"
              onChange={handleFileUpload}
              className="hidden"
            />

            {useDefaultPlan && pdfImages.length === 0 ? (
                /* Default plan display */
              <>
                {/* Enhanced toolbar for default plan */}
                <div className="absolute top-4 left-4 z-10 flex items-center gap-2 bg-white rounded-lg shadow-lg p-2">
                  <span className="text-sm px-2 text-gray-600">Default Plan</span>
                  <div className="w-px h-6 bg-gray-300 mx-1" />
                  
                  {/* Zoom controls */}
                  <Button variant="outline" size="sm" onClick={handleZoomOut}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <span className="text-sm px-2 min-w-[50px] text-center">{Math.round(scale * 100)}%</span>
                  <Button variant="outline" size="sm" onClick={handleZoomIn}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleResetZoom}>
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  
                  <div className="w-px h-6 bg-gray-300 mx-1" />
                  
                  {/* Drawing tools */}
                  <Button 
                    variant={isEditingPoints ? "default" : "outline"} 
                    size="sm"
                    onClick={() => setIsEditingPoints(!isEditingPoints)}
                    title="Edit Points"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  
                  <Button variant="outline" size="sm" onClick={handleUndo} title="Undo">
                    <Undo2 className="h-4 w-4" />
                  </Button>
                  
                  
                  <div className="w-px h-6 bg-gray-300 mx-1" />
                  <Button variant="outline" size="sm" onClick={handleUploadClick}>
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>

                {/* Default plan image display */}
                <div 
                  className="h-full overflow-auto flex items-center justify-center p-4 canvas-container"
                  onClick={handleCanvasClick}
                  onDoubleClick={handleDoubleClick}
                  onMouseDown={handleMouseDown}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseUp}
                  onMouseMove={handleMouseMove}
                  onContextMenu={(e) => e.preventDefault()} // Prevent context menu on middle click
                  style={{ 
                    cursor: isMiddleMouseDragging ? 'grabbing' : (isDrawing ? 'crosshair' : (scale > 1.0 ? 'grab' : 'default'))
                  }}
                >
                  <div className="relative inline-block">
                     <img
                       src={DEFAULT_PLAN_IMAGE}
                       alt="Default Floor Plan"
                       style={{
                         transform: `scale(${scale}) translate(${panOffset.x / scale}px, ${panOffset.y / scale}px)`,
                         transformOrigin: 'center center',
                         width: '600px',
                         height: '450px', // Match SVG exactly
                         willChange: 'transform', // Optimize for transform changes
                         userSelect: 'none', // Prevent text selection
                         pointerEvents: 'none' // Prevent image from being draggable/selectable
                       }}
                       className="border rounded-lg shadow-lg block"
                       draggable={false}
                     />
                    
                      {/* SVG overlay for drawing - positioned exactly over the image */}
                      <svg
                        className="absolute top-0 left-0"
                        style={{
                          width: '600px',
                          height: '450px',
                          transform: `scale(${scale}) translate(${panOffset.x / scale}px, ${panOffset.y / scale}px)`,
                          transformOrigin: 'center center',
                          pointerEvents: isDrawing ? 'all' : 'auto'
                        }}
                        viewBox="0 0 2400 1800"
                     >
                        {/* Render completed shapes */}
                        {shapes.length > 0 ? (
                          shapes.map((shape) => {
                            console.log('Rendering shape:', shape.id, 'with points:', shape.points.length, 'color:', shape.color);
                            return (
                              <g key={shape.id}>
                                <polygon
                                  points={shape.points.map(p => `${p.x},${p.y}`).join(' ')}
                                  fill={`${shape.color}40`}
                                  stroke={shape.color}
                                  strokeWidth={8 / scale}
                                  style={{ cursor: isEditingPoints ? 'move' : 'default' }}
                                />
                                {/* Render edit points when in edit mode */}
                                {isEditingPoints && shape.points.map((point, index) => (
                                  <circle
                                    key={`${shape.id}-point-${index}`}
                                    cx={point.x}
                                    cy={point.y}
                                    r={24 / scale}
                                    fill={selectedPoint?.shapeId === shape.id && selectedPoint?.pointIndex === index ? "#ff0000" : shape.color}
                                    stroke="white"
                                    strokeWidth={8 / scale}
                                    style={{ cursor: draggedPoint?.shapeId === shape.id && draggedPoint?.pointIndex === index ? 'grabbing' : 'grab', pointerEvents: 'all' }}
                                    onClick={(e) => handlePointClick(e, shape.id, index)}
                                    onMouseDown={(e) => handlePointMouseDown(e, shape.id, index)}
                                    onMouseUp={(e) => {
                                      e.stopPropagation();
                                      setDraggedPoint(null);
                                    }}
                                  />
                                ))}
                              </g>
                            );
                          })
                        ) : (
                          <text x="50" y="50" fill="red" fontSize="16">
                            No shapes found - Pending has {currentPending?.shapes?.length || 0} shapes
                          </text>
                        )}
                      
                      {/* Render current shape being drawn */}
                      {isDrawingShape && currentShape.length > 0 && (
                        <g>
                          {/* Draw lines between points */}
                          {currentShape.length > 1 && (
                             <polyline
                               points={currentShape.map(p => `${p.x},${p.y}`).join(' ')}
                               fill="none"
                               stroke={getShapeColor(drawingMode!)}
                               strokeWidth={8 / scale} // Scale stroke width inversely with zoom
                               strokeDasharray={`${20 / scale},${20 / scale}`} // Scale dash pattern with zoom
                             />
                          )}
                           {/* Show current points */}
                           {currentShape.map((point, index) => (
                             <circle
                               key={index}
                               cx={point.x}
                               cy={point.y}
                               r={16 / scale} // Scale point size inversely with zoom
                               fill={getShapeColor(drawingMode!)}
                             />
                           ))}
                          {/* Show preview line to first point if we have 3+ points */}
                          {currentShape.length >= 3 && (
                             <line
                               x1={currentShape[currentShape.length - 1].x}
                               y1={currentShape[currentShape.length - 1].y}
                               x2={currentShape[0].x}
                               y2={currentShape[0].y}
                               stroke={getShapeColor(drawingMode!)}
                               strokeWidth={8 / scale} // Scale stroke width inversely with zoom
                               strokeDasharray={`${12 / scale},${12 / scale}`} // Scale dash pattern with zoom
                               opacity="0.5"
                             />
                          )}
                        </g>
                      )}
                     </svg>
                   </div>
                 </div>
                 
                 {/* Info panel below the image */}
                 <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                   <p className="text-sm text-blue-800 font-medium mb-1">Sample Floor Plan</p>
                   <p className="text-xs text-blue-600">Click "Create New" to start drawing work areas</p>
                 </div>
              </>
            ) : pdfImages.length === 0 ? (
              /* Upload area */
              <div className="absolute inset-4 bg-gray-100 border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-lg font-medium mb-2">Floor Plan Drawing Area</p>
                  <p className="text-sm mb-4">Upload a PDF plan or start drawing work areas</p>
                  {isLoadingPdf ? (
                    <div className="text-blue-600">
                      <div className="animate-spin h-8 w-8 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
                      <p>Converting PDF to images...</p>
                    </div>
                  ) : (
                    <Button 
                      onClick={handleUploadClick}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload PDF Plan
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              /* PDF Image viewer area */
              <>
                {/* Image controls */}
                <div className="absolute top-4 left-4 z-10 flex items-center gap-2 bg-white rounded-lg shadow-lg p-2">
                  <Button variant="outline" size="sm" onClick={goToPrevPage} disabled={currentPageIndex <= 0}>
                    ←
                  </Button>
                  <span className="text-sm px-2">
                    {currentPageIndex + 1} / {numPages}
                  </span>
                  <Button variant="outline" size="sm" onClick={goToNextPage} disabled={currentPageIndex >= numPages - 1}>
                    →
                  </Button>
                  <div className="w-px h-6 bg-gray-300 mx-1" />
                  <Button variant="outline" size="sm" onClick={handleZoomOut}>
                    -
                  </Button>
                  <span className="text-sm px-2">{Math.round(scale * 100)}%</span>
                  <Button variant="outline" size="sm" onClick={handleZoomIn}>
                    +
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleResetZoom}>
                    Reset
                  </Button>
                  <div className="w-px h-6 bg-gray-300 mx-1" />
                  <Button variant="outline" size="sm" onClick={handleUploadClick}>
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>

                {/* Image display */}
                <div className="h-full overflow-auto flex items-center justify-center p-4">
                  {pdfError ? (
                    <div className="text-center text-red-500">
                      <p className="text-lg font-medium mb-2">Error converting PDF</p>
                      <p className="text-sm mb-4">{pdfError}</p>
                      <Button onClick={handleUploadClick} variant="outline">
                        Try Another File
                      </Button>
                    </div>
                  ) : pdfImages[currentPageIndex] ? (
                    <img
                      src={pdfImages[currentPageIndex]}
                      alt={`PDF Page ${currentPageIndex + 1}`}
                      style={{
                        transform: `scale(${scale})`,
                        maxWidth: 'none',
                        height: 'auto'
                      }}
                      className="transition-transform duration-200"
                    />
                  ) : (
                    <div className="text-center text-gray-500">
                      <p>Loading page...</p>
                    </div>
                  )}
                </div>
              </>
            )}

            {/* Drawing mode overlay */}
            {isDrawing && (
              <div className="absolute top-4 right-4 bg-orange-100 border border-orange-200 rounded-lg p-4 max-w-xs z-20">
                <h3 className="font-medium text-orange-800 mb-2">
                  Creating a {drawingMode?.toLowerCase()}:
                </h3>
                <p className="text-sm text-orange-700 mb-3">
                  Click anywhere on the plan to start drawing a {drawingMode?.toLowerCase()}. Create a polygon and close it by connecting the last point to the starting point.
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleCancelDrawing}
                  className="w-full"
                >
                  Cancel (ESC)
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Form panel with tabs */}
        <div className="w-80 border-l bg-background overflow-y-auto">
          <Tabs defaultValue="details" className="h-full">
            <TabsList className="grid w-full grid-cols-2 m-4">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="drawings">Drawings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="details" className="p-6 pt-0">
              <div className="space-y-6">
                {/* Work Area Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Work Area</span>
                    <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
                      {selectedWorkAreaType}
                    </Badge>
                    <span className="text-sm text-muted-foreground">{selectedLevel}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="sm" onClick={() => setShowDeleteDialog(true)}>
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <svg className="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </Button>
                  </div>
                </div>

                <Separator />

                {/* Work Area Name */}
                <div className="space-y-2">
                  <Label htmlFor="workAreaName">Work Area Name *</Label>
                  <Input
                    id="workAreaName"
                    value={currentPending?.area_name || ''}
                    onChange={(e) => updatePendingField('area_name', e.target.value)}
                    placeholder="Enter name"
                  />
                </div>

            {/* Level */}
            <div className="space-y-2">
              <Label>Level *</Label>
              <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Level 1">Level 1</SelectItem>
                  <SelectItem value="Level 2">Level 2</SelectItem>
                  <SelectItem value="Level 3">Level 3</SelectItem>
                  <SelectItem value="Level 4">Level 4</SelectItem>
                  <SelectItem value="Level 5">Level 5</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label>Category</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Apartment">Apartment</SelectItem>
                  <SelectItem value="Office">Office</SelectItem>
                  <SelectItem value="Commercial">Commercial</SelectItem>
                  <SelectItem value="Retail">Retail</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Type a tag"
                  onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                />
                <Button size="sm" onClick={handleAddTag}>Add</Button>
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {tags.map((tag) => (
                  <Badge key={tag.id} variant="secondary" className="flex items-center gap-1">
                    {tag.name}
                    <X className="h-3 w-3 cursor-pointer" onClick={() => handleRemoveTag(tag.id)} />
                  </Badge>
                ))}
              </div>
            </div>

            {/* Substrate Material */}
            <div className="space-y-2">
              <Label>Substrate Material</Label>
              <Select value={substrateMaterial} onValueChange={setSubstrateMaterial}>
                <SelectTrigger>
                  <SelectValue placeholder="Select material" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Concrete">Concrete</SelectItem>
                  <SelectItem value="Steel">Steel</SelectItem>
                  <SelectItem value="Wood">Wood</SelectItem>
                  <SelectItem value="Gypsum">Gypsum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* FRL */}
            <div className="space-y-4">
              <Label>FRL</Label>
              
              <div className="space-y-2">
                <Label>Colour *</Label>
                <Select value={frlColor} onValueChange={setFrlColor}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select color" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Pink">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-pink-400 rounded-full"></div>
                        Pink
                      </div>
                    </SelectItem>
                    <SelectItem value="Blue">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-blue-400 rounded-full"></div>
                        Blue
                      </div>
                    </SelectItem>
                    <SelectItem value="Green">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-green-400 rounded-full"></div>
                        Green
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div className="text-center">
                  <Button variant="outline" size="sm" className="w-full mb-1">▲</Button>
                  <Input
                    type="number"
                    value={frlValues.first}
                    onChange={(e) => setFrlValues(prev => ({ ...prev, first: parseInt(e.target.value) || 0 }))}
                    className="text-center"
                  />
                  <Button variant="outline" size="sm" className="w-full mt-1">▼</Button>
                </div>
                <div className="flex items-center justify-center text-lg">/</div>
                <div className="text-center">
                  <Button variant="outline" size="sm" className="w-full mb-1">▲</Button>
                  <Input
                    type="number"
                    value={frlValues.second}
                    onChange={(e) => setFrlValues(prev => ({ ...prev, second: parseInt(e.target.value) || 0 }))}
                    className="text-center"
                  />
                  <Button variant="outline" size="sm" className="w-full mt-1">▼</Button>
                </div>
                <div className="flex items-center justify-center text-lg">/</div>
                <div className="text-center">
                  <Button variant="outline" size="sm" className="w-full mb-1">▲</Button>
                  <Input
                    type="number"
                    value={frlValues.third}
                    onChange={(e) => setFrlValues(prev => ({ ...prev, third: parseInt(e.target.value) || 0 }))}
                    className="text-center"
                  />
                  <Button variant="outline" size="sm" className="w-full mt-1">▼</Button>
                </div>
              </div>
            </div>
              </div>
            </TabsContent>
            
            <TabsContent value="drawings" className="p-6 pt-0">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold">Drawing Sets</h2>
                  <span className="text-sm text-muted-foreground">
                    {drawingSets.length} sets
                  </span>
                </div>
                
                {/* Drawing Sets List */}
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {drawingSets.map((set) => (
                    <div
                      key={set.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        activeDrawingSet === set.id 
                          ? 'border-primary bg-primary/5' 
                          : 'border-border hover:bg-muted/50'
                      }`}
                      onClick={() => switchToDrawingSet(set.id)}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{set.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {set.shapes.length} shapes
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {set.created.toLocaleDateString()}
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Current Drawing Shapes */}
                 <div>
                   <div className="flex items-center justify-between mb-2">
                     <h3 className="font-medium">Current Shapes</h3>
                     <span className="text-sm text-muted-foreground">
                       {shapes.length} shapes
                     </span>
                   </div>
                   
                   {/* Debug info */}
                   <div className="mb-2 p-2 bg-muted rounded text-xs">
                     <div>Pending ID: {currentPending?.id || 'None'}</div>
                     <div>Project ID: {currentPending?.project_id || 'None'}</div>
                     <div>Shapes in pending: {currentPending?.shapes?.length || 0}</div>
                     <div>Shapes rendered: {shapes.length}</div>
                   </div>
                   
                   <div className="space-y-1 max-h-60 overflow-y-auto">
                    {shapes.map((shape) => (
                      <div
                        key={shape.id}
                        className="flex items-center justify-between p-2 border rounded hover:bg-muted/50"
                      >
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded"
                            style={{ backgroundColor: shape.color }}
                          />
                          <span className="text-sm">{shape.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span className="text-xs text-muted-foreground">
                            {shape.type}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteShape(shape.id)}
                            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    
                    {shapes.length === 0 && (
                      <div className="text-center text-muted-foreground text-sm py-4">
                        No shapes in current drawing
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <DialogTitle className="text-lg">
              Are you sure you want to delete this work area?
            </DialogTitle>
            <p className="text-sm text-muted-foreground mt-2">
              You will not be able undo this action.
            </p>
          </DialogHeader>
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Don't Delete
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteWorkArea}
              className="bg-red-600 hover:bg-red-700"
            >
              Yes, delete.
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CreateWorkArea;
