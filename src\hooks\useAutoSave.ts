import { useEffect, useCallback, useRef } from 'react';
import { useAppDispatch, useAppSelector } from './index';
import { saveDraft } from '@/store/slices/workAreasSlice';
import { WorkArea } from '@/types';

interface UseAutoSaveOptions {
  delay?: number; // Auto-save delay in milliseconds
  enabled?: boolean; // Whether auto-save is enabled
  onSave?: (draft: WorkArea) => void; // Callback when auto-save occurs
  onError?: (error: string) => void; // Callback when auto-save fails
  onlyOnDrawingChanges?: boolean; // Only auto-save when shapes change
}

export const useAutoSave = (options: UseAutoSaveOptions = {}) => {
  const {
    delay = 2000, // Default 2 seconds
    enabled = true,
    onSave,
    onError,
    onlyOnDrawingChanges = false
  } = options;

  const dispatch = useAppDispatch();
  const { currentWorkArea, autoSaveEnabled } = useAppSelector(state => state.workAreas);
  const currentDraft = currentWorkArea;
  
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSavedRef = useRef<string>('');
  const lastSavedShapesRef = useRef<string>('');

  const performAutoSave = useCallback(async () => {
    if (!currentDraft || !enabled || !autoSaveEnabled) return;

    // Check if draft has actually changed
    const currentDraftString = JSON.stringify(currentDraft);
    if (currentDraftString === lastSavedRef.current) return;

    // If onlyOnDrawingChanges is true, check if only shapes have changed
    if (onlyOnDrawingChanges) {
      const currentShapesString = JSON.stringify(currentDraft.shapes || []);
      if (currentShapesString === lastSavedShapesRef.current) return;
      lastSavedShapesRef.current = currentShapesString;
    }

    try {
      await dispatch(saveDraft(currentDraft)).unwrap();
      lastSavedRef.current = currentDraftString;
      onSave?.(currentDraft);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Auto-save failed';
      onError?.(errorMessage);
    }
  }, [currentDraft, enabled, autoSaveEnabled, dispatch, onSave, onError, onlyOnDrawingChanges]);

  const scheduleAutoSave = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (currentDraft && enabled && autoSaveEnabled) {
      timeoutRef.current = setTimeout(performAutoSave, delay);
    }
  }, [currentDraft, enabled, autoSaveEnabled, delay, performAutoSave]);

  // Schedule auto-save when currentDraft changes
  useEffect(() => {
    scheduleAutoSave();

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [scheduleAutoSave]);

  // Manual save function
  const manualSave = useCallback(async () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    await performAutoSave();
  }, [performAutoSave]);

  // Function to cancel pending auto-save
  const cancelAutoSave = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  return {
    manualSave,
    cancelAutoSave,
    isAutoSaveEnabled: enabled && autoSaveEnabled,
    hasUnsavedChanges: currentDraft ? JSON.stringify(currentDraft) !== lastSavedRef.current : false
  };
};