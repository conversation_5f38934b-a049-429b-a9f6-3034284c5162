<<<<<<<
import { configureStore } from '@reduxjs/toolkit';

=======
import { configureStore } from '@reduxjs/toolkit';

import usersReducer from './slices/usersSlice';

import projectsReducer from './slices/projectsSlice';

import workAreasReducer from './slices/workAreasSlice';

import firePenetrationsReducer from './slices/firePenetrationsSlice';

import authReducer from './slices/authSlice';

import permissionsReducer from './slices/permissionsSlice';

>>>>>>>


<<<<<<<
import usersReducer from './slices/usersSlice';

=======
export const store = configureStore({

  reducer: {

    auth: authReducer,

    permissions: permissionsReducer,

    users: usersReducer,

    projects: projectsReducer,

    workAreas: workAreasReducer,

    firePenetrations: firePenetrationsReducer,

  },

  middleware: (getDefaultMiddleware) =>

    getDefaultMiddleware({

      serializableCheck: {

        ignoredActions: ['persist/PERSIST'],

      },

    }),

});

>>>>>>>


import projectsReducer from './slices/projectsSlice';



import workAreasReducer from './slices/workAreasSlice';



import firePenetrationsReducer from './slices/firePenetrationsSlice';







export const store = configureStore({



  reducer: {



    users: usersReducer,



    projects: projectsReducer,



    workAreas: workAreasReducer,



    firePenetrations: firePenetrationsReducer,



  },



  middleware: (getDefaultMiddleware) =>



    getDefaultMiddleware({



      serializableCheck: {



        ignoredActions: ['persist/PERSIST'],



      },



    }),



});







export type RootState = ReturnType<typeof store.getState>;



export type AppDispatch = typeof store.dispatch;
