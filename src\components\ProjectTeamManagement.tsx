import { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { usePermissions } from "@/hooks/usePermissions";
import { fetchUsers } from "@/store/slices/usersSlice";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, UserPlus, UserMinus, Mail, Shield } from "lucide-react";
import { User, ProjectMember, MemberR<PERSON>, Discipline, MemberStatus } from "@/types";
import { toast } from "sonner";
import PermissionGate from "@/components/PermissionGate";

interface ProjectTeamManagementProps {
  projectId: number;
  projectMembers: ProjectMember[];
  onAddMember: (member: Omit<ProjectMember, 'id'>) => void;
  onRemoveMember: (memberId: number) => void;
  onUpdateMember: (memberId: number, updates: Partial<ProjectMember>) => void;
}

const ProjectTeamManagement = ({
  projectId,
  projectMembers,
  onAddMember,
  onRemoveMember,
  onUpdateMember,
}: ProjectTeamManagementProps) => {
  const dispatch = useAppDispatch();
  const { users } = useAppSelector((state) => state.users);
  const { canManageProjectTeam } = usePermissions();

  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [inviteForm, setInviteForm] = useState({
    user_id: "",
    role: "service_provider" as MemberRole,
    discipline: "electrical" as Discipline,
    permissions: {
      read: true,
      write: false,
      admin: false,
    },
  });

  useEffect(() => {
    dispatch(fetchUsers());
  }, [dispatch]);

  // Get users not already in the project
  const availableUsers = users.filter(
    user => !projectMembers.some(member => member.user_id === user.id)
  );

  const handleInviteMember = () => {
    if (!inviteForm.user_id) {
      toast.error("Please select a user to invite");
      return;
    }

    const newMember: Omit<ProjectMember, 'id'> = {
      project_id: projectId,
      user_id: parseInt(inviteForm.user_id),
      role: inviteForm.role,
      discipline: inviteForm.discipline,
      invited_by: 1, // Current user ID - should come from auth context
      status: "invited",
      permissions: inviteForm.permissions,
    };

    onAddMember(newMember);
    setIsInviteDialogOpen(false);
    setInviteForm({
      user_id: "",
      role: "service_provider",
      discipline: "electrical",
      permissions: {
        read: true,
        write: false,
        admin: false,
      },
    });
    toast.success("Team member invited successfully");
  };

  const handleRemoveMember = (memberId: number) => {
    if (confirm("Are you sure you want to remove this team member?")) {
      onRemoveMember(memberId);
      toast.success("Team member removed successfully");
    }
  };

  const handleUpdateMemberRole = (memberId: number, newRole: MemberRole) => {
    const permissions = {
      read: true,
      write: newRole !== "inspector",
      admin: newRole === "project_admin",
    };

    onUpdateMember(memberId, { role: newRole, permissions });
    toast.success("Member role updated successfully");
  };

  const handleToggleMemberStatus = (memberId: number, currentStatus: MemberStatus) => {
    const newStatus: MemberStatus = currentStatus === "active" ? "inactive" : "active";
    onUpdateMember(memberId, { status: newStatus });
    toast.success(`Member ${newStatus === "active" ? "activated" : "deactivated"} successfully`);
  };

  const getUserById = (userId: number) => {
    return users.find(user => user.id === userId);
  };

  const getRoleBadgeColor = (role: MemberRole) => {
    switch (role) {
      case 'project_admin': return 'bg-blue-100 text-blue-800';
      case 'service_provider': return 'bg-green-100 text-green-800';
      case 'inspector': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeColor = (status: MemberStatus) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'invited': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Project Team</h2>
          <p className="text-muted-foreground">Manage team members and their roles</p>
        </div>
        
        <PermissionGate requiredPermissions={['projects.manage_team']}>
          <Button 
            onClick={() => setIsInviteDialogOpen(true)}
            className="flex items-center gap-2"
            disabled={availableUsers.length === 0}
          >
            <UserPlus className="w-4 h-4" />
            Invite Member
          </Button>
        </PermissionGate>
      </div>

      {/* Team Members Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Member</TableHead>
                <TableHead>Company</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Discipline</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Permissions</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {projectMembers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    No team members yet. Invite members to get started.
                  </TableCell>
                </TableRow>
              ) : (
                projectMembers.map((member) => {
                  const user = getUserById(member.user_id);
                  return (
                    <TableRow key={member.id}>
                      <TableCell className="font-medium">
                        {user ? `${user.first_name} ${user.last_name}` : 'Unknown User'}
                        <div className="text-sm text-muted-foreground">
                          {user?.email}
                        </div>
                      </TableCell>
                      <TableCell>{user?.company_name}</TableCell>
                      <TableCell>
                        <PermissionGate 
                          requiredPermissions={['projects.assign_roles']}
                          fallback={
                            <Badge className={getRoleBadgeColor(member.role)}>
                              {member.role.replace('_', ' ')}
                            </Badge>
                          }
                        >
                          <Select
                            value={member.role}
                            onValueChange={(value) => handleUpdateMemberRole(member.id, value as MemberRole)}
                          >
                            <SelectTrigger className="w-auto">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="project_admin">Project Admin</SelectItem>
                              <SelectItem value="service_provider">Service Provider</SelectItem>
                              <SelectItem value="inspector">Inspector</SelectItem>
                            </SelectContent>
                          </Select>
                        </PermissionGate>
                      </TableCell>
                      <TableCell className="capitalize">
                        {member.discipline.replace('_', ' ')}
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusBadgeColor(member.status)}>
                          {member.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          {member.permissions.read && <Badge variant="outline">Read</Badge>}
                          {member.permissions.write && <Badge variant="outline">Write</Badge>}
                          {member.permissions.admin && <Badge variant="outline">Admin</Badge>}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <PermissionGate requiredPermissions={['projects.manage_team']}>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleToggleMemberStatus(member.id, member.status)}
                            >
                              {member.status === "active" ? (
                                <UserMinus className="w-4 h-4" />
                              ) : (
                                <UserPlus className="w-4 h-4" />
                              )}
                            </Button>
                          </PermissionGate>
                          
                          <PermissionGate requiredPermissions={['projects.manage_team']}>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRemoveMember(member.id)}
                              className="text-destructive hover:text-destructive"
                            >
                              <UserMinus className="w-4 h-4" />
                            </Button>
                          </PermissionGate>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Invite Member Dialog */}
      <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Invite Team Member</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="user_select">Select User</Label>
              <Select 
                value={inviteForm.user_id} 
                onValueChange={(value) => setInviteForm({ ...inviteForm, user_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a user to invite" />
                </SelectTrigger>
                <SelectContent>
                  {availableUsers.map((user) => (
                    <SelectItem key={user.id} value={user.id.toString()}>
                      {user.first_name} {user.last_name} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="role_select">Role</Label>
                <Select 
                  value={inviteForm.role} 
                  onValueChange={(value) => setInviteForm({ ...inviteForm, role: value as MemberRole })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="project_admin">Project Admin</SelectItem>
                    <SelectItem value="service_provider">Service Provider</SelectItem>
                    <SelectItem value="inspector">Inspector</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="discipline_select">Discipline</Label>
                <Select 
                  value={inviteForm.discipline} 
                  onValueChange={(value) => setInviteForm({ ...inviteForm, discipline: value as Discipline })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="electrical">Electrical</SelectItem>
                    <SelectItem value="plumbing">Plumbing</SelectItem>
                    <SelectItem value="dry_fire_services">Dry Fire Services</SelectItem>
                    <SelectItem value="hvac">HVAC</SelectItem>
                    <SelectItem value="wet_fire_services">Wet Fire Services</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleInviteMember}>
                Send Invitation
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProjectTeamManagement;
