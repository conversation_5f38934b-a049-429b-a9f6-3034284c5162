<<<<<<<
import { Toaster } from "@/components/ui/toaster";

=======
import { Toaster } from "@/components/ui/toaster";

import { Toaster as Sonner } from "@/components/ui/sonner";

import { TooltipProvider } from "@/components/ui/tooltip";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import { BrowserRouter, Routes, Route } from "react-router-dom";

import { Provider } from "react-redux";

import { store } from "@/store";

import ProtectedRoute from "@/components/ProtectedRoute";

import Index from "./pages/Index";

import Projects from "./pages/Projects";

import Settings from "./pages/Settings";

import LoginPage from "./pages/LoginPage";

import CreateProject from "./pages/CreateProject";

import CreateWorkArea from "./pages/CreateWorkArea";

import AdminDashboard from "./pages/AdminDashboard";

import OverlayManagement from "./pages/OverlayManagement";

import ProjectSetup from "./pages/ProjectSetup";

import PasswordRecovery from "./components/PasswordRecovery";

import NotFound from "./pages/NotFound";

>>>>>>>


import { Toaster as Sonner } from "@/components/ui/sonner";



<<<<<<<
import { TooltipProvider } from "@/components/ui/tooltip";

=======
const App = () => (

  <Provider store={store}>

    <QueryClientProvider client={queryClient}>

      <TooltipProvider>

        <Toaster />

        <Sonner />

        <BrowserRouter>

          <Routes>

            {/* Public routes */}

            <Route path="/login" element={<LoginPage />} />

            <Route path="/forgot-password" element={<PasswordRecovery />} />



            {/* Protected routes */}

            <Route

              path="/"

              element={

                <ProtectedRoute>

                  <Index />

                </ProtectedRoute>

              }

            />



            <Route

              path="/projects"

              element={

                <ProtectedRoute requiredPermissions={['projects.read']}>

                  <Projects />

                </ProtectedRoute>

              }

            />



            <Route

              path="/projects/create"

              element={

                <ProtectedRoute requiredPermissions={['projects.create']}>

                  <CreateProject />

                </ProtectedRoute>

              }

            />



            <Route

              path="/projects/:projectId/work-areas/create"

              element={

                <ProtectedRoute requiredPermissions={['work_areas.create']}>

                  <CreateWorkArea />

                </ProtectedRoute>

              }

            />



            <Route

              path="/projects/:projectId/overlays"

              element={

                <ProtectedRoute requiredPermissions={['overlays.read']}>

                  <OverlayManagement />

                </ProtectedRoute>

              }

            />



            <Route

              path="/projects/:projectId/setup"

              element={

                <ProtectedRoute requiredPermissions={['projects.read']}>

                  <ProjectSetup />

                </ProtectedRoute>

              }

            />



            <Route

              path="/admin"

              element={

                <ProtectedRoute

                  requiredRoles={['super_admin', 'project_admin']}

                  requiredPermissions={['admin.access']}

                  requireAllPermissions={false}

                >

                  <AdminDashboard />

                </ProtectedRoute>

              }

            />



            <Route

              path="/settings"

              element={

                <ProtectedRoute>

                  <Settings />

                </ProtectedRoute>

              }

            />



            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}

            <Route path="*" element={<NotFound />} />

          </Routes>

        </BrowserRouter>

      </TooltipProvider>

    </QueryClientProvider>

  </Provider>

);

>>>>>>>


import { QueryClient, QueryClientProvider } from "@tanstack/react-query";



import { BrowserRouter, Routes, Route } from "react-router-dom";



import { Provider } from "react-redux";



import { store } from "@/store";



import Index from "./pages/Index";



import Projects from "./pages/Projects";



import Settings from "./pages/Settings";



import LoginPage from "./pages/LoginPage";



import CreateProject from "./pages/CreateProject";



import CreateWorkArea from "./pages/CreateWorkArea";



import AdminDashboard from "./pages/AdminDashboard";



import NotFound from "./pages/NotFound";







const queryClient = new QueryClient();







const App = () => (



  <Provider store={store}>



    <QueryClientProvider client={queryClient}>



      <TooltipProvider>



        <Toaster />



        <Sonner />



        <BrowserRouter>



          <Routes>



            <Route path="/" element={<Index />} />



            <Route path="/login" element={<LoginPage />} />



            <Route path="/admin" element={<AdminDashboard />} />



            <Route path="/projects" element={<Projects />} />



            <Route path="/projects/create" element={<CreateProject />} />



            <Route path="/projects/:projectId/work-areas/create" element={<CreateWorkArea />} />



            <Route path="/settings" element={<Settings />} />



            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}



            <Route path="*" element={<NotFound />} />



          </Routes>



        </BrowserRouter>



      </TooltipProvider>



    </QueryClientProvider>



  </Provider>



);







export default App;



