import { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { usePermissions } from '@/hooks/usePermissions';
import { UserType } from '@/types';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredRoles?: UserType[];
  requiredPermissions?: string[];
  requireAllPermissions?: boolean; // true = AND logic, false = OR logic
  fallbackPath?: string;
  showAccessDenied?: boolean;
}

const ProtectedRoute = ({
  children,
  requireAuth = true,
  requiredRoles = [],
  requiredPermissions = [],
  requireAllPermissions = true,
  fallbackPath = '/login',
  showAccessDenied = true,
}: ProtectedRouteProps) => {
  const { isAuthenticated, user, loading } = useAuth();
  const { hasPermission, hasAllPermissions, hasAnyPermission } = usePermissions();
  const location = useLocation();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-connecta-cyan"></div>
      </div>
    );
  }

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check role requirements
  if (requiredRoles.length > 0 && user) {
    const hasRequiredRole = requiredRoles.includes(user.user_type);
    if (!hasRequiredRole) {
      if (showAccessDenied) {
        return (
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="max-w-md w-full">
              <Alert className="border-destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-center">
                  <div className="font-semibold mb-2">Access Denied</div>
                  <div className="text-sm text-muted-foreground">
                    You don't have the required role to access this page.
                    <br />
                    Required roles: {requiredRoles.join(', ')}
                    <br />
                    Your role: {user.user_type}
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          </div>
        );
      }
      return <Navigate to="/projects" replace />;
    }
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAllPermissions
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions);

    if (!hasRequiredPermissions) {
      if (showAccessDenied) {
        return (
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="max-w-md w-full">
              <Alert className="border-destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-center">
                  <div className="font-semibold mb-2">Access Denied</div>
                  <div className="text-sm text-muted-foreground">
                    You don't have the required permissions to access this page.
                    <br />
                    Required permissions: {requiredPermissions.join(', ')}
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          </div>
        );
      }
      return <Navigate to="/projects" replace />;
    }
  }

  // All checks passed, render the protected content
  return <>{children}</>;
};

export default ProtectedRoute;
