import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserType } from '@/types';

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface RolePermissions {
  role: UserType | 'guest';
  permissions: Permission[];
}

interface PermissionsState {
  rolePermissions: RolePermissions[];
  userPermissions: Record<number, Permission[]>; // user_id -> permissions
}

// Define all available permissions
const ALL_PERMISSIONS: Permission[] = [
  // User Management
  { id: 'users.create', name: 'Create Users', description: 'Create new users', resource: 'users', action: 'create' },
  { id: 'users.read', name: 'View Users', description: 'View user information', resource: 'users', action: 'read' },
  { id: 'users.update', name: 'Update Users', description: 'Update user information', resource: 'users', action: 'update' },
  { id: 'users.delete', name: 'Delete Users', description: 'Delete users', resource: 'users', action: 'delete' },
  { id: 'users.assign_roles', name: 'Assign Roles', description: 'Assign roles to users', resource: 'users', action: 'assign_roles' },
  { id: 'users.manage_permissions', name: 'Manage Permissions', description: 'Manage user permissions', resource: 'users', action: 'manage_permissions' },

  // Project Management
  { id: 'projects.create', name: 'Create Projects', description: 'Create new projects', resource: 'projects', action: 'create' },
  { id: 'projects.read', name: 'View Projects', description: 'View project information', resource: 'projects', action: 'read' },
  { id: 'projects.update', name: 'Update Projects', description: 'Update project information', resource: 'projects', action: 'update' },
  { id: 'projects.delete', name: 'Delete Projects', description: 'Delete projects', resource: 'projects', action: 'delete' },
  { id: 'projects.manage_team', name: 'Manage Team', description: 'Add/remove team members', resource: 'projects', action: 'manage_team' },
  { id: 'projects.assign_roles', name: 'Assign Project Roles', description: 'Assign project-specific roles', resource: 'projects', action: 'assign_roles' },

  // Plans & Overlays
  { id: 'plans.upload', name: 'Upload Plans', description: 'Upload floor plans', resource: 'plans', action: 'upload' },
  { id: 'plans.read', name: 'View Plans', description: 'View floor plans', resource: 'plans', action: 'read' },
  { id: 'plans.update', name: 'Update Plans', description: 'Update floor plans', resource: 'plans', action: 'update' },
  { id: 'plans.delete', name: 'Delete Plans', description: 'Delete floor plans', resource: 'plans', action: 'delete' },
  { id: 'overlays.create', name: 'Create Overlays', description: 'Create plan overlays', resource: 'overlays', action: 'create' },
  { id: 'overlays.read', name: 'View Overlays', description: 'View plan overlays', resource: 'overlays', action: 'read' },
  { id: 'overlays.update', name: 'Update Overlays', description: 'Update plan overlays', resource: 'overlays', action: 'update' },
  { id: 'overlays.delete', name: 'Delete Overlays', description: 'Delete plan overlays', resource: 'overlays', action: 'delete' },

  // Work Areas
  { id: 'work_areas.create', name: 'Create Work Areas', description: 'Create work areas', resource: 'work_areas', action: 'create' },
  { id: 'work_areas.read', name: 'View Work Areas', description: 'View work areas', resource: 'work_areas', action: 'read' },
  { id: 'work_areas.update', name: 'Update Work Areas', description: 'Update work areas', resource: 'work_areas', action: 'update' },
  { id: 'work_areas.delete', name: 'Delete Work Areas', description: 'Delete work areas', resource: 'work_areas', action: 'delete' },

  // Fire Penetrations
  { id: 'penetrations.create', name: 'Create Penetrations', description: 'Create fire penetrations', resource: 'penetrations', action: 'create' },
  { id: 'penetrations.read', name: 'View Penetrations', description: 'View fire penetrations', resource: 'penetrations', action: 'read' },
  { id: 'penetrations.update', name: 'Update Penetrations', description: 'Update fire penetrations', resource: 'penetrations', action: 'update' },
  { id: 'penetrations.delete', name: 'Delete Penetrations', description: 'Delete fire penetrations', resource: 'penetrations', action: 'delete' },
  { id: 'penetrations.inspect', name: 'Inspect Penetrations', description: 'Perform inspections', resource: 'penetrations', action: 'inspect' },
  { id: 'penetrations.approve', name: 'Approve Penetrations', description: 'Approve penetrations', resource: 'penetrations', action: 'approve' },

  // Admin Functions
  { id: 'admin.access', name: 'Admin Access', description: 'Access admin panel', resource: 'admin', action: 'access' },
  { id: 'admin.system_settings', name: 'System Settings', description: 'Manage system settings', resource: 'admin', action: 'system_settings' },
  { id: 'admin.audit_logs', name: 'Audit Logs', description: 'View audit logs', resource: 'admin', action: 'audit_logs' },

  // Reports
  { id: 'reports.generate', name: 'Generate Reports', description: 'Generate reports', resource: 'reports', action: 'generate' },
  { id: 'reports.export', name: 'Export Reports', description: 'Export reports', resource: 'reports', action: 'export' },
];

// Define role-based permissions
const DEFAULT_ROLE_PERMISSIONS: RolePermissions[] = [
  {
    role: 'super_admin',
    permissions: ALL_PERMISSIONS, // Super admin has all permissions
  },
  {
    role: 'project_admin',
    permissions: ALL_PERMISSIONS.filter(p => 
      p.resource !== 'admin' && 
      !['users.create', 'users.delete', 'users.assign_roles', 'users.manage_permissions'].includes(p.id)
    ),
  },
  {
    role: 'service_provider',
    permissions: ALL_PERMISSIONS.filter(p => 
      ['projects.read', 'plans.read', 'overlays.read', 'work_areas.create', 'work_areas.read', 
       'work_areas.update', 'penetrations.create', 'penetrations.read', 'penetrations.update',
       'reports.generate'].includes(p.id)
    ),
  },
  {
    role: 'inspector',
    permissions: ALL_PERMISSIONS.filter(p => 
      ['projects.read', 'plans.read', 'overlays.read', 'work_areas.read', 
       'penetrations.read', 'penetrations.inspect', 'penetrations.approve',
       'reports.generate', 'reports.export'].includes(p.id)
    ),
  },
  {
    role: 'guest',
    permissions: ALL_PERMISSIONS.filter(p => 
      ['projects.read', 'plans.read', 'overlays.read', 'work_areas.read', 
       'penetrations.read'].includes(p.id)
    ),
  },
];

const initialState: PermissionsState = {
  rolePermissions: DEFAULT_ROLE_PERMISSIONS,
  userPermissions: {},
};

const permissionsSlice = createSlice({
  name: 'permissions',
  initialState,
  reducers: {
    updateRolePermissions: (state, action: PayloadAction<{ role: UserType | 'guest'; permissions: Permission[] }>) => {
      const { role, permissions } = action.payload;
      const existingRoleIndex = state.rolePermissions.findIndex(rp => rp.role === role);
      
      if (existingRoleIndex >= 0) {
        state.rolePermissions[existingRoleIndex].permissions = permissions;
      } else {
        state.rolePermissions.push({ role, permissions });
      }
    },
    
    updateUserPermissions: (state, action: PayloadAction<{ userId: number; permissions: Permission[] }>) => {
      const { userId, permissions } = action.payload;
      state.userPermissions[userId] = permissions;
    },
    
    addPermissionToRole: (state, action: PayloadAction<{ role: UserType | 'guest'; permission: Permission }>) => {
      const { role, permission } = action.payload;
      const rolePermissions = state.rolePermissions.find(rp => rp.role === role);
      
      if (rolePermissions && !rolePermissions.permissions.find(p => p.id === permission.id)) {
        rolePermissions.permissions.push(permission);
      }
    },
    
    removePermissionFromRole: (state, action: PayloadAction<{ role: UserType | 'guest'; permissionId: string }>) => {
      const { role, permissionId } = action.payload;
      const rolePermissions = state.rolePermissions.find(rp => rp.role === role);
      
      if (rolePermissions) {
        rolePermissions.permissions = rolePermissions.permissions.filter(p => p.id !== permissionId);
      }
    },
    
    resetPermissions: (state) => {
      state.rolePermissions = DEFAULT_ROLE_PERMISSIONS;
      state.userPermissions = {};
    },
  },
});

export const {
  updateRolePermissions,
  updateUserPermissions,
  addPermissionToRole,
  removePermissionFromRole,
  resetPermissions,
} = permissionsSlice.actions;

export default permissionsSlice.reducer;

// Selectors
export const selectRolePermissions = (state: { permissions: PermissionsState }, role: UserType | 'guest') => {
  return state.permissions.rolePermissions.find(rp => rp.role === role)?.permissions || [];
};

export const selectUserPermissions = (state: { permissions: PermissionsState }, userId: number) => {
  return state.permissions.userPermissions[userId] || [];
};

export const selectAllPermissions = () => ALL_PERMISSIONS;
