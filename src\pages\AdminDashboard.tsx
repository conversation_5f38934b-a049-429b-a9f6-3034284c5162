<<<<<<<
import DashboardLayout from "@/components/DashboardLayout";

=======
import DashboardLayout from "@/components/DashboardLayout";

import ManageProjects from "@/components/ManageProjects";

import EnhancedManageUsers from "@/components/EnhancedManageUsers";

import { useSearchParams } from "react-router-dom";

import { usePermissions } from "@/hooks/usePermissions";

import { Alert, AlertDescription } from "@/components/ui/alert";

import { AlertTriangle } from "lucide-react";

>>>>>>>


<<<<<<<
import ManageProjects from "@/components/ManageProjects";

=======
const AdminDashboard = () => {

  const [searchParams] = useSearchParams();

  const activeTab = searchParams.get("tab") || "projects";

  const { canAccessAdmin, hasPermission } = usePermissions();

>>>>>>>


<<<<<<<
import ManageUsers from "@/components/ManageUsers";

=======
  if (!canAccessAdmin) {

    return (

      <DashboardLayout>

        <div className="flex items-center justify-center min-h-[60vh] p-4">

          <Alert className="border-destructive max-w-md">

            <AlertTriangle className="h-4 w-4" />

            <AlertDescription className="text-center">

              <div className="font-semibold mb-2">Access Denied</div>

              <div className="text-sm text-muted-foreground">

                You don't have permission to access the admin panel.

              </div>

            </AlertDescription>

          </Alert>

        </div>

      </DashboardLayout>

    );

  }



  return (

    <DashboardLayout>

      <div className="p-8">

        {activeTab === "projects" && <ManageProjects />}

        {activeTab === "users" && hasPermission('users.read') && <EnhancedManageUsers />}

        {activeTab === "users" && !hasPermission('users.read') && (

          <Alert className="border-destructive">

            <AlertTriangle className="h-4 w-4" />

            <AlertDescription>

              You don't have permission to manage users.

            </AlertDescription>

          </Alert>

        )}

      </div>

    </DashboardLayout>

  );

};

>>>>>>>


import { useSearchParams } from "react-router-dom";







const AdminDashboard = () => {



  const [searchParams] = useSearchParams();



  const activeTab = searchParams.get("tab") || "projects";







  return (



    <DashboardLayout>



      <div className="p-8">



        {activeTab === "projects" && <ManageProjects />}



        {activeTab === "users" && <ManageUsers />}



      </div>



    </DashboardLayout>



  );



};







export default AdminDashboard;
