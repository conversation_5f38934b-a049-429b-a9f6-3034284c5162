import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import StatusDropdown from "@/components/StatusDropdown";
import { useAppSelector } from "@/hooks";
import { Link } from "react-router-dom";
import { FileText, Layers } from "lucide-react";

type Status = "Draft" | "Published" | "Completed";

interface Project {
  id: string;
  name: string;
  address: string;
  lastUpdated: string;
  status: Status;
  image?: string;
}

interface ProjectCardProps {
  project: Project;
  onStatusChange?: (projectId: string, status: Status) => void;
  onManage?: (projectId: string) => void;
}

const ProjectCard = ({ project, onStatusChange, onManage }: ProjectCardProps) => {
  const { workAreas } = useAppSelector((state) => state.workAreas);

  // Find pending work areas for this project
  const projectPending = workAreas.filter(workArea =>
    workArea.project_id === parseInt(project.id) &&
    workArea.level_id === 2 &&
    workArea.pending === true
  );

  const pendingWithShapes = projectPending.filter(pending =>
    pending.shapes && pending.shapes.length > 0
  );

  return (
    <div className="project-card">
      <div className="flex items-center gap-4">
        {/* Project Image */}
        <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
          {project.image ? (
            <img 
              src={project.image} 
              alt={project.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-8 h-8 bg-connecta-light-cyan rounded"></div>
          )}
        </div>

        {/* Project Info */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-foreground truncate">{project.name}</h3>
          <p className="text-muted-foreground text-sm truncate">{project.address}</p>
          
          {/* Pending indicators */}
          {pendingWithShapes.length > 0 && (
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="secondary" className="text-xs">
                <Layers className="w-3 h-3 mr-1" />
                {pendingWithShapes.length} saved overlay{pendingWithShapes.length > 1 ? 's' : ''}
              </Badge>
            </div>
          )}
        </div>

        {/* Last Updated */}
        <div className="text-center min-w-[120px]">
          <p className="text-sm text-muted-foreground">Last Updated</p>
          <p className="text-sm font-medium text-foreground">{project.lastUpdated}</p>
        </div>

        {/* Status */}
        <div className="min-w-[120px]">
          <StatusDropdown
            currentStatus={project.status}
            onStatusChange={(status) => onStatusChange?.(project.id, status)}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 min-w-[160px]">
          {pendingWithShapes.length > 0 && (
            <Link to={`/projects/${project.id}/work-areas/create`}>
              <Button
                variant="default"
                size="sm"
                className="text-xs"
              >
                <FileText className="w-3 h-3 mr-1" />
                View Pending
              </Button>
            </Link>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onManage?.(project.id)}
          >
            Manage
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;