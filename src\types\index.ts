// Database schema types based on the ERD

export type UserType = 'super_admin' | 'project_admin' | 'service_provider' | 'inspector';
export type Discipline = 'electrical' | 'plumbing' | 'dry_fire_services' | 'hvac' | 'wet_fire_services';
export type UserStatus = 'active' | 'inactive' | 'pending';
export type ProjectStatus = 'draft' | 'published' | 'completed' | 'archived' | 'inactive';
export type MemberRole = 'project_admin' | 'service_provider' | 'inspector';
export type MemberStatus = 'invited' | 'active' | 'inactive';
export type LevelType = 'basement' | 'ground' | 'floor' | 'roof';
export type AreaType = 'floor' | 'wall' | 'ceiling';
export type AreaCategory = 'apartment' | 'commercial' | 'common_area' | 'mechanical';
export type SubstrateType = 'wall' | 'floor' | 'ceiling';
export type SubstrateMaterial = 'concrete' | 'hebel' | 'steel' | 'timber';
export type ServiceType = 'electrical' | 'plumbing' | 'dry_fire_services' | 'hvac' | 'wet_fire_services';
export type PenetrationStatus = 'not_started' | 'labelled' | 'images_uploaded' | 'interim_pass' | 'ready_for_inspection' | 'amendments_required' | 'final_pass';
export type ImageType = 'site_image' | 'system_image' | 'final_image';
export type FileType = 'document' | 'drawing' | 'certificate' | 'other';
export type OverlayType = 'architectural' | 'structural' | 'electrical' | 'plumbing' | 'dry_fire_services' | 'hvac' | 'wet_fire_services';
export type InvitationStatus = 'pending' | 'accepted' | 'expired' | 'cancelled';

export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  email_verified_at?: string;
  password: string;
  company_name: string;
  phone: string;
  user_type: UserType;
  discipline: Discipline;
  status: UserStatus;
  date_joined: string;
  last_login?: string;
}

export interface Project {
  id: number;
  project_code: string;
  name: string;
  address: string;
  project_admin_id: number;
  client_company: string;
  status: ProjectStatus;
  start_date: string;
  end_date?: string;
  image?: string;
  lastUpdated?: string;
}

export interface ProjectMember {
  id: number;
  project_id: number;
  user_id: number;
  role: MemberRole;
  discipline: Discipline;
  invited_by: number;
  status: MemberStatus;
  permissions: Record<string, any>;
}

export interface ProjectInvitation {
  id: number;
  project_id: number;
  email: string;
  invited_by: number;
  role: MemberRole;
  discipline: Discipline;
  token: string;
  status: InvitationStatus;
  expires_at: string;
}

export interface ProjectLevel {
  id: number;
  project_id: number;
  level_name: string;
  level_number: number;
  level_type: LevelType;
}

export interface WorkArea {
  id: number | string; // Allow string for draft IDs
  project_id: number;
  level_id: number;
  area_code: string;
  area_name: string;
  area_type: AreaType;
  category: AreaCategory;
  frl_rating: string;
  coordinates?: { x: number; y: number }[];
  color?: string;
  pending: boolean; // New pending column instead of draft
  substrate_type?: SubstrateType;
  substrate_material?: SubstrateMaterial;
  tags?: string[];
  shapes?: WorkAreaShape[]; // Add shapes for drafts
  plan_overlay_path?: string;
  drawing_data?: any;
  auto_saved_at?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
}


export interface WorkAreaShape {
  id: string;
  type: 'Floor' | 'Wall' | 'Ceiling';
  points: { x: number; y: number }[];
  completed: boolean;
  color: string;
  name: string;
  area_type?: AreaType;
  category?: AreaCategory;
  frl_rating?: string;
  substrate_type?: SubstrateType;
  substrate_material?: SubstrateMaterial;
}


export interface PlanOverlay {
  id: number;
  project_id: number;
  level_id: number;
  overlay_type: OverlayType;
  file_path: string;
  is_base_plan: boolean;
  uploaded_by: number;
}

export interface FirePenetration {
  id: number;
  project_id: number;
  service_id: string;
  work_area_id: number;
  service_provider_id: number;
  inspector_id: number;
  service_type: ServiceType;
  fire_stopping_system_id: number;
  frl_rating: string;
  status: PenetrationStatus;
  coordinates?: { x: number; y: number };
}

export interface FireStoppingSystem {
  id: number;
  matrix_id: string;
  manufacturer_id: number;
  system_name: string;
  service_types: ServiceType[];
  frl_rating: string;
}

export interface Manufacturer {
  id: number;
  name: string;
  contact_person: string;
  phone: string;
  email: string;
}

export interface PenetrationImage {
  id: number;
  penetration_id: number;
  uploaded_by: number;
  file_path: string;
  image_type: ImageType;
}

export interface PenetrationComment {
  id: number;
  penetration_id: number;
  user_id: number;
  comment: string;
  created_at: string;
}

export interface PenetrationFile {
  id: number;
  penetration_id: number;
  uploaded_by: number;
  file_path: string;
  file_type: FileType;
}

export interface Notification {
  id: number;
  user_id: number;
  type: string;
  title: string;
  message: string;
  read_at?: string;
  created_at: string;
}

export interface UserSetting {
  id: number;
  user_id: number;
  email_notifications: boolean;
  setting_key: string;
  setting_value: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  success: boolean;
}