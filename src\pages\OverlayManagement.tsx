import { useParams } from "react-router-dom";
import DashboardLayout from "@/components/DashboardLayout";
import UnifiedOverlayManager from "@/components/UnifiedOverlayManager";

const OverlayManagement = () => {
  const { projectId } = useParams<{ projectId: string }>();

  if (!projectId) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <h1 className="text-2xl font-bold text-red-600">Invalid Project ID</h1>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <UnifiedOverlayManager projectId={parseInt(projectId)} />
      </div>
    </DashboardLayout>
  );
};

export default OverlayManagement;