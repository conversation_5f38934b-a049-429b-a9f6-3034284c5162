import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { FirePenetration } from '@/types';
import { firePenetrationsApi } from '@/api/mockApi';

interface FirePenetrationsState {
  penetrations: FirePenetration[];
  currentPenetration: FirePenetration | null;
  loading: boolean;
  error: string | null;
}

const initialState: FirePenetrationsState = {
  penetrations: [],
  currentPenetration: null,
  loading: false,
  error: null,
};

// Async thunks
export const fetchPenetrationsByProjectId = createAsyncThunk(
  'firePenetrations/fetchByProjectId',
  async (projectId: number) => {
    const response = await firePenetrationsApi.getByProjectId(projectId);
    return response.data;
  }
);

export const fetchPenetrationsByWorkAreaId = createAsyncThunk(
  'firePenetrations/fetchByWorkAreaId',
  async (workAreaId: number) => {
    const response = await firePenetrationsApi.getByWorkAreaId(workAreaId);
    return response.data;
  }
);

export const createPenetration = createAsyncThunk(
  'firePenetrations/createPenetration',
  async (penetrationData: Omit<FirePenetration, 'id'>) => {
    const response = await firePenetrationsApi.create(penetrationData);
    return response.data;
  }
);

export const updatePenetration = createAsyncThunk(
  'firePenetrations/updatePenetration',
  async ({ id, penetrationData }: { id: number; penetrationData: Partial<FirePenetration> }) => {
    const response = await firePenetrationsApi.update(id, penetrationData);
    return response.data;
  }
);

const firePenetrationsSlice = createSlice({
  name: 'firePenetrations',
  initialState,
  reducers: {
    setCurrentPenetration: (state, action) => {
      state.currentPenetration = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    // Local state management for drawing
    addPenetrationPoint: (state, action) => {
      const { penetrationId, coordinates } = action.payload;
      const penetration = state.penetrations.find(p => p.id === penetrationId);
      if (penetration) {
        penetration.coordinates = coordinates;
      }
    },
    updatePenetrationCoordinates: (state, action) => {
      const { penetrationId, coordinates } = action.payload;
      const penetration = state.penetrations.find(p => p.id === penetrationId);
      if (penetration) {
        penetration.coordinates = coordinates;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch penetrations by project ID
      .addCase(fetchPenetrationsByProjectId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPenetrationsByProjectId.fulfilled, (state, action) => {
        state.loading = false;
        state.penetrations = action.payload;
      })
      .addCase(fetchPenetrationsByProjectId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch penetrations';
      })
      // Fetch penetrations by work area ID
      .addCase(fetchPenetrationsByWorkAreaId.fulfilled, (state, action) => {
        state.penetrations = action.payload;
      })
      // Create penetration
      .addCase(createPenetration.fulfilled, (state, action) => {
        state.penetrations.push(action.payload);
      })
      // Update penetration
      .addCase(updatePenetration.fulfilled, (state, action) => {
        const index = state.penetrations.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.penetrations[index] = action.payload;
        }
      });
  },
});

export const { 
  setCurrentPenetration, 
  clearError, 
  addPenetrationPoint, 
  updatePenetrationCoordinates 
} = firePenetrationsSlice.actions;
export default firePenetrationsSlice.reducer;