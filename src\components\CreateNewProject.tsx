import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, ChevronDown, Plus, GripVertical, Trash2, Bell, UserPlus, Filter, Check, Edit, User, Menu, X } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Link, useNavigate } from "react-router-dom";
import { useAppDispatch } from "@/hooks/useAppDispatch";
import { useAppSelector } from "@/hooks/useAppSelector";
import { createProject } from "@/store/slices/projectsSlice";
import { createUser } from "@/store/slices/usersSlice";
import { createWorkArea } from "@/store/slices/workAreasSlice";
import { useToast } from "@/hooks/use-toast";
import connectaIcon from "@/assets/connecta-logo-icon.png";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// Sortable Level Item Component
const SortableLevelItem = ({ level, isSelected, onSelect, onDelete, onNameChange }: {
  level: { id: number; name: string; type: string };
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onNameChange: (name: string) => void;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: level.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      onClick={onSelect}
      className={`flex items-center gap-3 p-3 rounded border cursor-pointer transition-colors ${
        isSelected 
          ? "border-connecta-cyan bg-connecta-cyan/10 text-connecta-navy" 
          : "border-border hover:bg-muted/50"
      }`}
    >
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing"
      >
        <GripVertical className="w-4 h-4 text-muted-foreground" />
      </div>
      <span className="flex-1">{level.name}</span>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDelete();
        }}
        className="text-red-500 hover:text-red-700"
      >
        <Trash2 className="w-4 h-4" />
      </button>
    </div>
  );
};

const CreateNewProject = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { toast } = useToast();
  const { loading: projectLoading } = useAppSelector((state) => state.projects);
  const { loading: userLoading } = useAppSelector((state) => state.users);
  
  const [currentStep, setCurrentStep] = useState(1);
  const [projectName, setProjectName] = useState("");
  const [locationAddress, setLocationAddress] = useState("");
  const [clientCompany, setClientCompany] = useState("");
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  
  // Step 2 state
  const [levels, setLevels] = useState([
    { id: 1, name: "Level 1", type: "level" },
    { id: 2, name: "Ground", type: "ground" }
  ]);
  const [selectedLevel, setSelectedLevel] = useState(1);
  const [levelName, setLevelName] = useState("Level 1");
  const [planName, setPlanName] = useState("");
  const [planFile, setPlanFile] = useState<string | null>(null);
  const [overlayName, setOverlayName] = useState("");
  const [overlayColor, setOverlayColor] = useState("Pink");
  
  // Step 3 state
  const [activeTab, setActiveTab] = useState("members");
  const [inviteEmail, setInviteEmail] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [teamMembers, setTeamMembers] = useState([
    {
      name: "Jack Mitchell",
      company: "Sydney Electricals",
      discipline: "Electrical",
      status: "Pending",
      serviceProvider: true,
      inspector: false,
      other: false
    }
  ]);
  
  // Filter state
  const [filters, setFilters] = useState({
    name: "All",
    company: "All", 
    discipline: "All",
    serviceProvider: "No",
    inspector: "No",
    other: "No"
  });
  
  // Work Areas state
  const [workAreas, setWorkAreas] = useState([
    {
      id: 1,
      name: "Living",
      type: "Floor",
      level: "Level 1",
      category: "Apartment",
      frl: "-/120/120",
      selected: false
    },
    {
      id: 2,
      name: "Wall 0001",
      type: "Wall",
      level: "Level 1",
      category: "Apartment",
      frl: "-/120/120",
      selected: false
    },
    {
      id: 3,
      name: "Dining",
      type: "Ceiling",
      level: "Level 1",
      category: "Apartment",
      frl: "-/120/120",
      selected: false
    },
    {
      id: 4,
      name: "Kitchen",
      type: "Floor",
      level: "Level 1",
      category: "Apartment",
      frl: "-/120/120",
      selected: false
    },
    {
      id: 5,
      name: "Laundry",
      type: "Floor",
      level: "Level 1",
      category: "Apartment",
      frl: "-/120/120",
      selected: false
    },
    {
      id: 6,
      name: "1/2 Bathroom",
      type: "Ceiling",
      level: "Level 1",
      category: "Apartment",
      frl: "-/120/120",
      selected: false
    },
    {
      id: 7,
      name: "Entrance",
      type: "Floor",
      level: "Level 1",
      category: "Apartment",
      frl: "-/120/120",
      selected: false
    },
    {
      id: 8,
      name: "Bedroom 1",
      type: "Ceiling",
      level: "Level 1",
      category: "Apartment",
      frl: "-/120/120",
      selected: false
    }
  ]);
  
  const [selectAllWorkAreas, setSelectAllWorkAreas] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showWorkAreaFilters, setShowWorkAreaFilters] = useState(false);
  const [workAreaFilters, setWorkAreaFilters] = useState({
    level: { all: true, level1: false, groundFloor: false, basement1: false, basement2: false },
    workAreaType: { all: true, wall: false, floor: false, ceiling: false },
    category: { all: true, apartment: false, balcony: false, lobby: false },
    frl: { first: "All", second: "All", third: "All" }
  });
  
  // Invite modal state
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [inviteForm, setInviteForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    company: "",
    discipline: ""
  });

  const steps = [
    { number: 1, title: "Project Details", active: currentStep === 1 },
    { number: 2, title: "Base Plans & Overlays", active: currentStep === 2 },
    { number: 3, title: "Teams and Roles", active: currentStep === 3 },
    { number: 4, title: "Work Areas", active: currentStep === 4 },
  ];

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Step 2 functions
  const addNewLevel = () => {
    const newId = Math.max(...levels.map(l => l.id)) + 1;
    setLevels([...levels, { id: newId, name: `Level ${newId}`, type: "level" }]);
  };

  const deleteLevel = (id: number) => {
    if (levels.length > 1) {
      setLevels(levels.filter(l => l.id !== id));
      if (selectedLevel === id) {
        setSelectedLevel(levels[0].id);
      }
    }
  };

  const updateLevelName = (id: number, name: string) => {
    setLevels(levels.map(l => l.id === id ? { ...l, name } : l));
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setLevels((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const createOverlay = () => {
    console.log("Create overlay:", { name: overlayName, color: overlayColor });
    setOverlayName("");
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setCoverImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleNext = async () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    } else if (currentStep === 4) {
      // Create the project and all work areas
      await handleCreateProject();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCreateProject = async () => {
    try {
      // Create the project
      const projectData = {
        project_code: `PROJ${Date.now()}`,
        name: projectName,
        address: locationAddress,
        client_company: clientCompany,
        status: 'draft' as const,
        start_date: new Date().toISOString().split('T')[0],
        end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        project_admin_id: 1 // Current user
      };
      
      const result = await dispatch(createProject(projectData));
      
      if (createProject.fulfilled.match(result)) {
        const createdProject = result.payload;
        
        // Create all selected work areas
        const selectedWorkAreas = workAreas.filter(area => area.selected);
        for (const area of selectedWorkAreas) {
          await dispatch(createWorkArea({
            project_id: createdProject.id,
            level_id: 1, // Default level
            area_code: `WA${area.id}`,
            area_name: area.name,
            area_type: area.type.toLowerCase() as 'floor' | 'wall' | 'ceiling',
            category: area.category.toLowerCase() as 'apartment' | 'commercial' | 'common_area' | 'mechanical',
            frl_rating: area.frl,
            pending: false,
          }));
        }
        
        toast({
          title: "Project Created",
          description: `Project "${projectName}" has been created successfully with ${selectedWorkAreas.length} work areas.`,
        });
        
        // Navigate to the created project
        navigate(`/projects/${createdProject.id}/work-areas/create`);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create project. Please try again.",
        variant: "destructive",
      });
    }
  };
  
  // Work area functions
  const handleSelectAllWorkAreas = (checked: boolean) => {
    setSelectAllWorkAreas(checked);
    setWorkAreas(workAreas.map(area => ({ ...area, selected: checked })));
  };
  
  const handleWorkAreaSelect = (id: number, checked: boolean) => {
    const updatedAreas = workAreas.map(area => 
      area.id === id ? { ...area, selected: checked } : area
    );
    setWorkAreas(updatedAreas);
    setSelectAllWorkAreas(updatedAreas.every(area => area.selected));
  };
  
  const handleDeleteSelected = () => {
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = () => {
    setWorkAreas(workAreas.filter(area => !area.selected));
    setSelectAllWorkAreas(false);
    setShowDeleteModal(false);
  };

  const handleDuplicateSelected = () => {
    setShowDuplicateModal(true);
  };

  const handleConfirmDuplicate = () => {
    const selectedAreas = workAreas.filter(area => area.selected);
    const duplicatedAreas = selectedAreas.map(area => ({
      ...area,
      id: Math.max(...workAreas.map(a => a.id)) + Math.random(),
      selected: false
    }));
    setWorkAreas([...workAreas.map(area => ({ ...area, selected: false })), ...duplicatedAreas]);
    setSelectAllWorkAreas(false);
    setShowDuplicateModal(false);
  };

  const getSelectedCount = () => workAreas.filter(area => area.selected).length;

  // Invite team member functions
  const handleInviteModalOpen = () => {
    setShowInviteModal(true);
  };

  const handleInviteFormChange = (field: string, value: string) => {
    setInviteForm(prev => ({ ...prev, [field]: value }));
  };

  const handleInviteSubmit = async () => {
    try {
      // Create/invite the user
      const userData = {
        first_name: inviteForm.firstName,
        last_name: inviteForm.lastName,
        email: inviteForm.email,
        password: 'temp123', // Temporary password
        company_name: inviteForm.company,
        discipline: inviteForm.discipline.toLowerCase() as any,
        user_type: 'service_provider' as const,
        status: 'pending' as const,
        phone: '',
        date_joined: new Date().toISOString(),
        last_login: null
      };
      
      await dispatch(createUser(userData));
      
      // Add to local team members for display
      const newMember = {
        name: `${inviteForm.firstName} ${inviteForm.lastName}`,
        company: inviteForm.company,
        discipline: inviteForm.discipline,
        status: "Pending",
        serviceProvider: true,
        inspector: false,
        other: false
      };
      setTeamMembers([...teamMembers, newMember]);
      
      // Reset form and show success
      setInviteForm({
        firstName: "",
        lastName: "",
        email: "",
        company: "",
        discipline: ""
      });
      setShowInviteModal(false);
      setShowSuccessModal(true);
      
      toast({
        title: "Invitation Sent",
        description: `Invitation sent to ${inviteForm.email}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send invitation. Please try again.",
        variant: "destructive",
      });
    }
  };

  const isInviteFormValid = inviteForm.firstName && inviteForm.lastName && inviteForm.email && inviteForm.company && inviteForm.discipline;

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Hamburger Menu */}
            <Sheet open={showMobileMenu} onOpenChange={setShowMobileMenu}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu className="w-5 h-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-64 bg-white p-0">
                <div className="flex flex-col h-full">
                  <div className="p-6 border-b border-border">
                    <div className="flex items-center gap-3">
                      <img src={connectaIcon} alt="ConnectaBuild" className="w-8 h-8" />
                      <h1 className="text-xl font-bold">
                        <span className="text-connecta-navy">Connecta</span>
                        <span className="text-connecta-cyan">Build</span>
                      </h1>
                    </div>
                  </div>
                  <nav className="flex-1 p-4">
                    <div className="space-y-2">
                      <Link 
                        to="/projects" 
                        className="flex items-center gap-3 px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted rounded-md transition-colors"
                        onClick={() => setShowMobileMenu(false)}
                      >
                        Projects
                      </Link>
                      <Link 
                        to="/create-project" 
                        className="flex items-center gap-3 px-3 py-2 text-sm font-medium text-foreground bg-muted rounded-md"
                        onClick={() => setShowMobileMenu(false)}
                      >
                        Create New Project
                      </Link>
                      <Link 
                        to="/settings" 
                        className="flex items-center gap-3 px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted rounded-md transition-colors"
                        onClick={() => setShowMobileMenu(false)}
                      >
                        Settings
                      </Link>
                    </div>
                  </nav>
                </div>
              </SheetContent>
            </Sheet>

            {/* Logo */}
            <div className="flex items-center gap-3">
              <img src={connectaIcon} alt="ConnectaBuild" className="w-8 h-8" />
              <h1 className="text-xl font-bold">
                <span className="text-connecta-navy">Connecta</span>
                <span className="text-connecta-cyan">Build</span>
              </h1>
            </div>
          </div>
          
          {/* Notification */}
          <div className="flex items-center gap-4">
            <Popover open={showNotifications} onOpenChange={setShowNotifications}>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                  <Bell className="w-6 h-6 text-muted-foreground" />
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">10</span>
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 bg-white border border-border shadow-lg" align="end">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-foreground">Notifications</h3>
                    <span className="text-xs text-muted-foreground">10 new</span>
                  </div>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {[...Array(10)].map((_, i) => (
                      <div key={i} className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-foreground">
                            New team member invited
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            Jack Mitchell has been invited to Project Alpha
                          </p>
                          <span className="text-xs text-muted-foreground">2 minutes ago</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="border-t border-border pt-3">
                    <Button variant="ghost" className="w-full text-sm text-connecta-cyan hover:text-connecta-cyan/80">
                      View all notifications
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
          <Link to="/projects" className="hover:text-connecta-navy">Projects</Link>
          <span>/</span>
          <span>Create New Project</span>
        </div>

        {/* Page Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-8 gap-4">
          <h1 className="text-3xl font-bold text-foreground">Create New Project</h1>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <span className="text-sm text-muted-foreground">Auto-saving draft...</span>
            <Button onClick={handleCreateProject} className="btn-primary">
              Save Project
            </Button>
          </div>
        </div>

        {/* Step Indicator */}
        <div className="flex flex-col md:flex-row md:items-center mb-8 md:border-b border-border">
          {steps.map((step) => (
            <div key={step.number} className="relative cursor-pointer md:flex-1" onClick={() => setCurrentStep(step.number)}>
              <div className={`flex items-center gap-3 p-4 md:justify-center md:pb-3 md:px-6 md:py-3 transition-colors duration-150 border-l-4 md:border-l-0 md:border-b-0 ${
                step.active 
                  ? "text-connecta-navy bg-connecta-cyan/10 font-medium border-l-connecta-cyan md:border-l-transparent" 
                  : "text-muted-foreground hover:text-foreground hover:bg-muted/30 border-l-transparent"
              }`}>
                <span className="font-medium">Step {step.number}</span>
                <span>
                  {step.title}
                </span>
              </div>
              {/* Active step underline - only on desktop */}
              {step.active && (
                <div className="hidden md:block absolute bottom-0 left-0 right-0 h-0.5 bg-connecta-cyan"></div>
              )}
            </div>
          ))}
        </div>

        {/* Section Title with underline */}
        <div className="mb-8">
          <div className="relative inline-block">
            <h2 className="text-xl font-bold text-foreground pb-3">
              {currentStep === 1 && "Project Details"}
              {currentStep === 2 && "Base Plans & Overlays"}
              {currentStep === 3 && "Teams and Roles"}
              {currentStep === 4 && "Work Areas"}
            </h2>
            <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-connecta-cyan"></div>
          </div>
        </div>

        {/* Form Content */}
        <div className="bg-card border border-border rounded-lg p-8">
          {currentStep === 1 && (
            <div className="space-y-8">
              <div className="border border-border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-foreground mb-6">Basic Details</h3>
                
                <div className="space-y-6">
                  {/* Project Name */}
                  <div className="space-y-2">
                    <Label htmlFor="projectName" className="text-foreground font-medium">
                      Project Name
                    </Label>
                    <Input
                      id="projectName"
                      value={projectName}
                      onChange={(e) => setProjectName(e.target.value)}
                      placeholder="Add here"
                      className="search-input"
                    />
                  </div>

                  {/* Location Address */}
                  <div className="space-y-2">
                    <Label htmlFor="locationAddress" className="text-foreground font-medium">
                      Location Address (Google Maps)
                    </Label>
                    <Select value={locationAddress} onValueChange={setLocationAddress}>
                      <SelectTrigger className="search-input">
                        <SelectValue placeholder="E.g. 1 George Street, 2000, Sydney NSW Australia" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                        <SelectItem value="8-oaks-avenue">8 Oaks Avenue, Dee Why, 2099 NSW</SelectItem>
                        <SelectItem value="17-victoria-avenue">17 Victoria Avenue, Chatswood, NSW 2067</SelectItem>
                        <SelectItem value="15-smith-street">15 Smith Street, Parramatta, NSW 2150</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Project Cover Image */}
                  <div className="space-y-2">
                    <Label className="text-foreground font-medium">
                      Project Cover Image (max. 1:1 ratio, max. 200×200px)
                    </Label>
                    
                    {imagePreview ? (
                      <div className="flex items-center gap-4">
                        <div className="w-20 h-20 rounded-lg overflow-hidden border border-border">
                          <img 
                            src={imagePreview} 
                            alt="Project cover" 
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-foreground">
                            {coverImage?.name || "deewhysuites.jpg"}
                          </p>
                          <button 
                            onClick={() => {
                              setCoverImage(null);
                              setImagePreview(null);
                            }}
                            className="text-connecta-cyan text-sm hover:underline"
                          >
                            Replace Upload
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="border-2 border-dashed border-border rounded-lg p-12 text-center relative">
                        <div className="flex flex-col items-center gap-4">
                          <Upload className="w-8 h-8 text-muted-foreground" />
                          <div>
                            <button className="text-connecta-cyan hover:underline font-medium">
                              Click to upload
                            </button>
                            <span className="text-muted-foreground"> or drag and drop</span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            SVG, PNG, JPG or GIF (max. 1:1 ratio, max. 200×200px)
                          </p>
                        </div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 2 - Base Plans & Overlays */}
          {currentStep === 2 && (
            <div className="space-y-8">
              {/* Base Plans Section */}
              <div className="border border-border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-foreground mb-2">Base Plans</h3>
                <p className="text-muted-foreground mb-6">This is where you upload base plans which sit in the background.</p>
                
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Levels List */}
                  <div className="lg:col-span-1">
                    <Button 
                      onClick={addNewLevel}
                      variant="outline" 
                      className="w-full mb-4 flex items-center gap-2"
                    >
                      <Plus className="w-4 h-4" />
                      Add New Level
                    </Button>
                    
                    <DndContext 
                      sensors={sensors}
                      collisionDetection={closestCenter}
                      onDragEnd={handleDragEnd}
                    >
                      <SortableContext 
                        items={levels.map(l => l.id)} 
                        strategy={verticalListSortingStrategy}
                      >
                        <div className="space-y-2">
                          {levels.map((level) => (
                            <SortableLevelItem
                              key={level.id}
                              level={level}
                              isSelected={selectedLevel === level.id}
                              onSelect={() => {
                                setSelectedLevel(level.id);
                                setLevelName(level.name);
                              }}
                              onDelete={() => deleteLevel(level.id)}
                              onNameChange={(name) => updateLevelName(level.id, name)}
                            />
                          ))}
                        </div>
                      </SortableContext>
                    </DndContext>
                  </div>
                  
                  {/* Level Details */}
                  <div className="lg:col-span-2 space-y-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label className="text-foreground font-medium">Level Name</Label>
                        <Input
                          value={levelName}
                          onChange={(e) => {
                            setLevelName(e.target.value);
                            updateLevelName(selectedLevel, e.target.value);
                          }}
                          className="search-input"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label className="text-foreground font-medium">Plan Name</Label>
                        <Input
                          value={planName}
                          onChange={(e) => setPlanName(e.target.value)}
                          placeholder="Architectural Plan"
                          className="search-input"
                        />
                      </div>
                    </div>
                    
                    {planFile ? (
                      <div className="flex items-center justify-between p-4 bg-muted/30 rounded border">
                        <span className="text-sm">{planFile}</span>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setPlanFile(null)}
                        >
                          Re-upload
                        </Button>
                      </div>
                    ) : (
                      <div className="text-center">
                        <Button 
                          onClick={() => setPlanFile("Architectural-base-plan-level-1.pdf")}
                          variant="outline"
                          className="flex items-center gap-2"
                        >
                          <Plus className="w-4 h-4" />
                          Add Base Plan
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Overlays Section */}
              <div className="border border-border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-foreground mb-2">Overlays</h3>
                <p className="text-muted-foreground mb-6">This is where you set up disciplines that are shown as overlays.</p>
                
                <div className="grid grid-cols-1 md:grid-cols-12 gap-4 mb-6">
                  <div className="md:col-span-6 space-y-2">
                    <Label className="text-foreground font-medium">Overlay Name</Label>
                    <Input
                      value={overlayName}
                      onChange={(e) => setOverlayName(e.target.value)}
                      placeholder="Enter Overlay Name"
                      className="search-input"
                    />
                  </div>
                  
                  <div className="md:col-span-3 space-y-2">
                    <Label className="text-foreground font-medium">Colour *</Label>
                    <Select value={overlayColor} onValueChange={setOverlayColor}>
                      <SelectTrigger className="search-input">
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 bg-pink-500 rounded-full"></div>
                          <SelectValue />
                        </div>
                      </SelectTrigger>
                      <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                        <SelectItem value="Pink">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 bg-pink-500 rounded-full"></div>
                            Pink
                          </div>
                        </SelectItem>
                        <SelectItem value="Blue">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                            Blue
                          </div>
                        </SelectItem>
                        <SelectItem value="Green">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                            Green
                          </div>
                        </SelectItem>
                        <SelectItem value="Yellow">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                            Yellow
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="md:col-span-3 flex items-end">
                    <Button
                      onClick={createOverlay}
                      disabled={!overlayName}
                      variant="outline"
                      className="w-full flex items-center gap-2"
                    >
                      <Plus className="w-4 h-4" />
                      Create Overlay
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 3 - Teams and Roles */}
          {currentStep === 3 && (
            <div className="space-y-8">
              <div className="border border-border rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-foreground">Teams and Roles</h3>
                  <Button 
                    className="btn-primary flex items-center gap-2"
                    onClick={handleInviteModalOpen}
                  >
                    <UserPlus className="w-4 h-4" />
                    Invite
                  </Button>
                </div>
                
                {/* Tab Navigation */}
                <div className="flex items-center gap-6 mb-6 border-b border-border">
                  <button 
                    className={`pb-3 px-1 font-medium transition-colors ${
                      activeTab === 'members' 
                        ? 'text-connecta-cyan border-b-2 border-connecta-cyan' 
                        : 'text-muted-foreground hover:text-foreground'
                    }`}
                    onClick={() => setActiveTab('members')}
                  >
                    Members
                  </button>
                  <button 
                    className={`pb-3 px-1 font-medium transition-colors ${
                      activeTab === 'invited' 
                        ? 'text-connecta-cyan border-b-2 border-connecta-cyan' 
                        : 'text-muted-foreground hover:text-foreground'
                    }`}
                    onClick={() => setActiveTab('invited')}
                  >
                    Invited
                  </button>
                </div>

                {/* Invite Section */}
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-foreground mb-4">Invite New or Existing Team Members</h4>
                  
                  <div className="flex items-center gap-3 mb-4">
                    <div className="flex-1 relative">
                      <Select value={inviteEmail} onValueChange={setInviteEmail}>
                        <SelectTrigger className="search-input">
                          <SelectValue placeholder="Type name or email address" />
                        </SelectTrigger>
                        <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                          <SelectItem value="<EMAIL>">Jack Mitchell - <EMAIL></SelectItem>
                          <SelectItem value="<EMAIL>">Sarah Jones - <EMAIL></SelectItem>
                          <SelectItem value="<EMAIL>">Mike Smith - <EMAIL></SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setShowFilters(!showFilters)}
                      className="flex items-center gap-2"
                    >
                      <Filter className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Filters Panel */}
                  {showFilters && (
                    <div className="bg-muted/30 border border-border rounded-lg p-6 mb-4">
                      <h5 className="font-semibold text-foreground mb-4">Filters</h5>
                      <div className="space-y-6">
                        <div>
                          <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                            Name
                            <ChevronDown className="w-4 h-4" />
                          </Label>
                          <Select value={filters.name} onValueChange={(value) => setFilters(prev => ({ ...prev, name: value }))}>
                            <SelectTrigger className="search-input">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                              <SelectItem value="All">All</SelectItem>
                              <SelectItem value="Jack Mitchell">Jack Mitchell</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                            Company
                            <ChevronDown className="w-4 h-4" />
                          </Label>
                          <Select value={filters.company} onValueChange={(value) => setFilters(prev => ({ ...prev, company: value }))}>
                            <SelectTrigger className="search-input">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                              <SelectItem value="All">All</SelectItem>
                              <SelectItem value="Sydney Electricals">Sydney Electricals</SelectItem>
                              <SelectItem value="Metro Plumbing">Metro Plumbing</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                            Discipline
                            <ChevronDown className="w-4 h-4" />
                          </Label>
                          <Select value={filters.discipline} onValueChange={(value) => setFilters(prev => ({ ...prev, discipline: value }))}>
                            <SelectTrigger className="search-input">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                              <SelectItem value="All">All</SelectItem>
                              <SelectItem value="Electrical">Electrical</SelectItem>
                              <SelectItem value="Plumbing">Plumbing</SelectItem>
                              <SelectItem value="HVAC">HVAC</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                            Service Provider
                            <ChevronDown className="w-4 h-4" />
                          </Label>
                          <div className="space-y-2">
                            <label className="flex items-center gap-2">
                              <input 
                                type="radio" 
                                name="serviceProvider" 
                                value="Yes"
                                checked={filters.serviceProvider === "Yes"}
                                onChange={(e) => setFilters(prev => ({ ...prev, serviceProvider: e.target.value }))}
                                className="w-4 h-4"
                              />
                              <span className="text-sm">Yes</span>
                            </label>
                            <label className="flex items-center gap-2">
                              <input 
                                type="radio" 
                                name="serviceProvider" 
                                value="No"
                                checked={filters.serviceProvider === "No"}
                                onChange={(e) => setFilters(prev => ({ ...prev, serviceProvider: e.target.value }))}
                                className="w-4 h-4"
                              />
                              <span className="text-sm">No</span>
                            </label>
                          </div>
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                            Inspector
                            <ChevronDown className="w-4 h-4" />
                          </Label>
                          <div className="space-y-2">
                            <label className="flex items-center gap-2">
                              <input 
                                type="radio" 
                                name="inspector" 
                                value="Yes"
                                checked={filters.inspector === "Yes"}
                                onChange={(e) => setFilters(prev => ({ ...prev, inspector: e.target.value }))}
                                className="w-4 h-4"
                              />
                              <span className="text-sm">Yes</span>
                            </label>
                            <label className="flex items-center gap-2">
                              <input 
                                type="radio" 
                                name="inspector" 
                                value="No"
                                checked={filters.inspector === "No"}
                                onChange={(e) => setFilters(prev => ({ ...prev, inspector: e.target.value }))}
                                className="w-4 h-4"
                              />
                              <span className="text-sm">No</span>
                            </label>
                          </div>
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                            Other
                            <ChevronDown className="w-4 h-4" />
                          </Label>
                          <div className="space-y-2">
                            <label className="flex items-center gap-2">
                              <input 
                                type="radio" 
                                name="other" 
                                value="Yes"
                                checked={filters.other === "Yes"}
                                onChange={(e) => setFilters(prev => ({ ...prev, other: e.target.value }))}
                                className="w-4 h-4"
                              />
                              <span className="text-sm">Yes</span>
                            </label>
                            <label className="flex items-center gap-2">
                              <input 
                                type="radio" 
                                name="other" 
                                value="No"
                                checked={filters.other === "No"}
                                onChange={(e) => setFilters(prev => ({ ...prev, other: e.target.value }))}
                                className="w-4 h-4"
                              />
                              <span className="text-sm">No</span>
                            </label>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center mt-6">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setFilters({
                            name: "All",
                            company: "All", 
                            discipline: "All",
                            serviceProvider: "No",
                            inspector: "No",
                            other: "No"
                          })}
                        >
                          Reset
                        </Button>
                        <Button 
                          size="sm" 
                          className="btn-primary"
                          onClick={() => setShowFilters(false)}
                        >
                          OK
                        </Button>
                      </div>
                    </div>
                  )}

                  <div className="mb-4">
                    <Label className="text-sm font-medium text-foreground mb-2 block">Manage Permissions</Label>
                  </div>

                  {/* Team Members Table */}
                  {teamMembers.length > 0 ? (
                    <div className="border border-border rounded-lg overflow-hidden">
                      <table className="w-full">
                        <thead className="bg-muted/50">
                          <tr>
                            <th className="text-left p-3 text-sm font-medium text-foreground">Name</th>
                            <th className="text-left p-3 text-sm font-medium text-foreground">Company</th>
                            <th className="text-left p-3 text-sm font-medium text-foreground">Discipline</th>
                            {activeTab === 'members' ? (
                              <>
                                <th className="text-center p-3 text-sm font-medium text-foreground">Service Provider</th>
                                <th className="text-center p-3 text-sm font-medium text-foreground">Inspector</th>
                                <th className="text-center p-3 text-sm font-medium text-foreground">Other</th>
                                <th className="text-center p-3 text-sm font-medium text-foreground">Actions</th>
                              </>
                            ) : (
                              <>
                                <th className="text-left p-3 text-sm font-medium text-foreground">Status</th>
                                <th className="text-center p-3 text-sm font-medium text-foreground">Actions</th>
                              </>
                            )}
                          </tr>
                        </thead>
                        <tbody>
                          {teamMembers.map((member, index) => (
                            <tr key={index} className="border-t border-border">
                              <td className="p-3 text-sm text-foreground">{member.name}</td>
                              <td className="p-3 text-sm text-foreground">{member.company}</td>
                              <td className="p-3 text-sm text-foreground">{member.discipline}</td>
                              {activeTab === 'members' ? (
                                <>
                                  <td className="p-3 text-center">
                                    <Switch 
                                      checked={member.serviceProvider || false}
                                      onCheckedChange={(checked) => {
                                        const updatedMembers = [...teamMembers];
                                        updatedMembers[index] = { ...member, serviceProvider: checked };
                                        setTeamMembers(updatedMembers);
                                      }}
                                    />
                                  </td>
                                  <td className="p-3 text-center">
                                    <Switch 
                                      checked={member.inspector || false}
                                      onCheckedChange={(checked) => {
                                        const updatedMembers = [...teamMembers];
                                        updatedMembers[index] = { ...member, inspector: checked };
                                        setTeamMembers(updatedMembers);
                                      }}
                                    />
                                  </td>
                                  <td className="p-3 text-center">
                                    <Switch 
                                      checked={member.other || false}
                                      onCheckedChange={(checked) => {
                                        const updatedMembers = [...teamMembers];
                                        updatedMembers[index] = { ...member, other: checked };
                                        setTeamMembers(updatedMembers);
                                      }}
                                    />
                                  </td>
                                  <td className="p-3">
                                    <div className="flex items-center justify-center gap-2">
                                      <button className="text-connecta-cyan hover:text-connecta-cyan/80">
                                        <Edit className="w-4 h-4" />
                                      </button>
                                      <button className="text-connecta-cyan hover:text-connecta-cyan/80">
                                        <User className="w-4 h-4" />
                                      </button>
                                      <button className="text-red-500 hover:text-red-600">
                                        <Trash2 className="w-4 h-4" />
                                      </button>
                                    </div>
                                  </td>
                                </>
                              ) : (
                                <>
                                  <td className="p-3">
                                    <span className={`text-sm px-2 py-1 rounded ${
                                      member.status === 'Pending' 
                                        ? 'bg-orange-100 text-orange-700' 
                                        : 'bg-green-100 text-green-700'
                                    }`}>
                                      {member.status}
                                    </span>
                                  </td>
                                  <td className="p-3">
                                    <div className="flex items-center justify-center gap-2">
                                      <button className="text-connecta-cyan hover:text-connecta-cyan/80">
                                        <Edit className="w-4 h-4" />
                                      </button>
                                      <button className="text-connecta-cyan hover:text-connecta-cyan/80">
                                        <User className="w-4 h-4" />
                                      </button>
                                      <button className="text-red-500 hover:text-red-600">
                                        <Trash2 className="w-4 h-4" />
                                      </button>
                                    </div>
                                  </td>
                                </>
                              )}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-12 text-muted-foreground">
                      <p>You currently have no members</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Step 4 - Work Areas */}
          {currentStep === 4 && (
            <div className="space-y-8">
              <h3 className="text-lg font-semibold text-foreground mb-6">Work Areas</h3>
              
              <div className="border border-border rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h4 className="text-lg font-semibold text-foreground">Create New Work Areas</h4>
                  <div className="flex items-center gap-4">
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={selectAllWorkAreas}
                        onChange={(e) => handleSelectAllWorkAreas(e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm text-muted-foreground">Select all</span>
                    </label>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="gap-2"
                      onClick={() => setShowWorkAreaFilters(!showWorkAreaFilters)}
                    >
                      <Filter className="w-4 h-4" />
                    </Button>
                    {getSelectedCount() > 0 && (
                      <>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={handleDuplicateSelected}
                          className="gap-2"
                        >
                          Duplicate
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={handleDeleteSelected}
                          className="gap-2 text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                          Delete Selected
                        </Button>
                      </>
                    )}
                    <Button 
                      size="sm"
                      className="btn-primary"
                    >
                      Create Work Areas
                    </Button>
                  </div>
                </div>

                {/* Filters Panel */}
                {showWorkAreaFilters && (
                  <div className="bg-muted/30 border border-border rounded-lg p-6 mb-4">
                    <h5 className="font-semibold text-foreground mb-4">Filters</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      {/* Level Filter */}
                      <div>
                        <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                          Level
                          <ChevronDown className="w-4 h-4" />
                        </Label>
                        <div className="space-y-2">
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.level.all}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                level: {
                                  all: e.target.checked,
                                  level1: false,
                                  groundFloor: false,
                                  basement1: false,
                                  basement2: false
                                }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">All</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.level.level1}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                level: { ...prev.level, level1: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Level 1</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.level.groundFloor}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                level: { ...prev.level, groundFloor: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Ground Floor</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.level.basement1}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                level: { ...prev.level, basement1: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Basement 1</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.level.basement2}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                level: { ...prev.level, basement2: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Basement 2</span>
                          </label>
                        </div>
                      </div>

                      {/* Work Area Type Filter */}
                      <div>
                        <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                          Work Area Type
                          <ChevronDown className="w-4 h-4" />
                        </Label>
                        <div className="space-y-2">
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.workAreaType.all}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                workAreaType: {
                                  all: e.target.checked,
                                  wall: false,
                                  floor: false,
                                  ceiling: false
                                }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">All</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.workAreaType.wall}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                workAreaType: { ...prev.workAreaType, wall: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Wall</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.workAreaType.floor}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                workAreaType: { ...prev.workAreaType, floor: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Floor</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.workAreaType.ceiling}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                workAreaType: { ...prev.workAreaType, ceiling: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Ceiling</span>
                          </label>
                        </div>
                      </div>

                      {/* Category Filter */}
                      <div>
                        <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                          Category
                          <ChevronDown className="w-4 h-4" />
                        </Label>
                        <div className="space-y-2">
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.category.all}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                category: {
                                  all: e.target.checked,
                                  apartment: false,
                                  balcony: false,
                                  lobby: false
                                }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">All</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.category.apartment}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                category: { ...prev.category, apartment: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Apartment</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.category.balcony}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                category: { ...prev.category, balcony: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Balcony</span>
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={workAreaFilters.category.lobby}
                              onChange={(e) => setWorkAreaFilters(prev => ({
                                ...prev,
                                category: { ...prev.category, lobby: e.target.checked, all: false }
                              }))}
                              className="rounded border-border"
                            />
                            <span className="text-sm text-foreground">Lobby</span>
                          </label>
                        </div>
                      </div>

                      {/* FRL Filter */}
                      <div>
                        <Label className="text-sm font-medium text-foreground mb-2 block flex items-center justify-between">
                          FRL
                          <ChevronDown className="w-4 h-4" />
                        </Label>
                        <div className="space-y-3">
                          <Select 
                            value={workAreaFilters.frl.first} 
                            onValueChange={(value) => setWorkAreaFilters(prev => ({
                              ...prev,
                              frl: { ...prev.frl, first: value }
                            }))}
                          >
                            <SelectTrigger className="search-input">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                              <SelectItem value="All">All</SelectItem>
                              <SelectItem value="-">-</SelectItem>
                              <SelectItem value="60">60</SelectItem>
                              <SelectItem value="90">90</SelectItem>
                              <SelectItem value="120">120</SelectItem>
                            </SelectContent>
                          </Select>
                          
                          <Select 
                            value={workAreaFilters.frl.second} 
                            onValueChange={(value) => setWorkAreaFilters(prev => ({
                              ...prev,
                              frl: { ...prev.frl, second: value }
                            }))}
                          >
                            <SelectTrigger className="search-input">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                              <SelectItem value="All">All</SelectItem>
                              <SelectItem value="-">-</SelectItem>
                              <SelectItem value="60">60</SelectItem>
                              <SelectItem value="90">90</SelectItem>
                              <SelectItem value="120">120</SelectItem>
                            </SelectContent>
                          </Select>
                          
                          <Select 
                            value={workAreaFilters.frl.third} 
                            onValueChange={(value) => setWorkAreaFilters(prev => ({
                              ...prev,
                              frl: { ...prev.frl, third: value }
                            }))}
                          >
                            <SelectTrigger className="search-input">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                              <SelectItem value="All">All</SelectItem>
                              <SelectItem value="-">-</SelectItem>
                              <SelectItem value="60">60</SelectItem>
                              <SelectItem value="90">90</SelectItem>
                              <SelectItem value="120">120</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-start mt-6">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => {
                          setWorkAreaFilters({
                            level: { all: true, level1: false, groundFloor: false, basement1: false, basement2: false },
                            workAreaType: { all: true, wall: false, floor: false, ceiling: false },
                            category: { all: true, apartment: false, balcony: false, lobby: false },
                            frl: { first: "All", second: "All", third: "All" }
                          });
                        }}
                        className="text-muted-foreground"
                      >
                        Reset
                      </Button>
                    </div>
                  </div>
                )}

                {/* Work Areas List */}
                {workAreas.length > 0 ? (
                  <div className="space-y-4">
                    {workAreas.map((area) => (
                      <div key={area.id} className="border border-border rounded-lg p-4 flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <input
                            type="checkbox"
                            checked={area.selected}
                            onChange={(e) => handleWorkAreaSelect(area.id, e.target.checked)}
                            className="rounded"
                          />
                          <div className="flex-1">
                            <h5 className="font-medium text-foreground mb-1">{area.name}</h5>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span>Type: <span className="text-foreground">{area.type}</span></span>
                              <span>Level: <span className="text-foreground">{area.level}</span></span>
                              <span>Category: <span className="text-foreground">{area.category}</span></span>
                              <span>FRL: <span className="text-foreground">{area.frl}</span></span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <button className="p-2 text-muted-foreground hover:text-foreground border border-border rounded">
                            <Trash2 className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-muted-foreground hover:text-foreground border border-border rounded">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-muted-foreground hover:text-foreground border border-border rounded">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    <p>You currently have no work areas</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={currentStep === 1}
            className="btn-outline"
          >
            Back
          </Button>
          
          <Button
            onClick={handleNext}
            className="btn-primary"
          >
            {currentStep === 4 ? "Create Work Area" : "Next"}
          </Button>
        </div>
      </div>

      {/* Invite Team Member Modal */}
      <Dialog open={showInviteModal} onOpenChange={setShowInviteModal}>
        <DialogContent className="sm:max-w-[500px] bg-white">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold text-foreground">Invite New User</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 mt-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-foreground">First Name</Label>
              <Input
                value={inviteForm.firstName}
                onChange={(e) => handleInviteFormChange('firstName', e.target.value)}
                className="search-input"
              />
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-foreground">Last Name</Label>
              <Input
                value={inviteForm.lastName}
                onChange={(e) => handleInviteFormChange('lastName', e.target.value)}
                className="search-input"
              />
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-foreground">Email Address</Label>
              <Input
                type="email"
                value={inviteForm.email}
                onChange={(e) => handleInviteFormChange('email', e.target.value)}
                className="search-input"
              />
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-foreground">Company Name</Label>
              <Select value={inviteForm.company} onValueChange={(value) => handleInviteFormChange('company', value)}>
                <SelectTrigger className="search-input">
                  <SelectValue placeholder="-select-" />
                </SelectTrigger>
                <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                  <SelectItem value="Sydney Electricals">Sydney Electricals</SelectItem>
                  <SelectItem value="Metro Plumbing">Metro Plumbing</SelectItem>
                  <SelectItem value="ABC Construction">ABC Construction</SelectItem>
                  <SelectItem value="BuildCorp">BuildCorp</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-foreground">Discipline</Label>
              <Select value={inviteForm.discipline} onValueChange={(value) => handleInviteFormChange('discipline', value)}>
                <SelectTrigger className="search-input">
                  <SelectValue placeholder="-select-" />
                </SelectTrigger>
                <SelectContent className="bg-white border border-border shadow-lg z-[var(--z-dropdown)]">
                  <SelectItem value="Electrical">
                    <div className="flex items-center gap-2">
                      <span className="inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                      Electrical
                    </div>
                  </SelectItem>
                  <SelectItem value="Plumbing">
                    <div className="flex items-center gap-2">
                      <span className="inline-block w-2 h-2 bg-green-500 rounded-full"></span>
                      Plumbing
                    </div>
                  </SelectItem>
                  <SelectItem value="HVAC">
                    <div className="flex items-center gap-2">
                      <span className="inline-block w-2 h-2 bg-orange-500 rounded-full"></span>
                      HVAC
                    </div>
                  </SelectItem>
                  <SelectItem value="Structural">
                    <div className="flex items-center gap-2">
                      <span className="inline-block w-2 h-2 bg-purple-500 rounded-full"></span>
                      Structural
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex gap-3 mt-6">
            <Button 
              variant="outline" 
              onClick={() => setShowInviteModal(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleInviteSubmit}
              disabled={!isInviteFormValid}
              className={`flex-1 ${isInviteFormValid ? 'btn-primary' : 'bg-muted text-muted-foreground cursor-not-allowed'}`}
            >
              Send
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Success Modal */}
      <Dialog open={showSuccessModal} onOpenChange={setShowSuccessModal}>
        <DialogContent className="sm:max-w-[400px] bg-white">
          <div className="text-center space-y-4 py-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-foreground">Invitation successfully sent!</h3>
            <Button 
              onClick={() => setShowSuccessModal(false)}
              className="btn-primary px-8"
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent className="sm:max-w-[500px] bg-white">
          <div className="text-center space-y-4 py-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground">
              Are you sure you want to delete {getSelectedCount()} selected work areas?
            </h3>
            <p className="text-muted-foreground">You will not be able to undo this action.</p>
            <div className="flex gap-3 mt-6">
              <Button 
                variant="outline" 
                onClick={() => setShowDeleteModal(false)}
                className="flex-1"
              >
                Don't Delete
              </Button>
              <Button 
                onClick={handleConfirmDelete}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white"
              >
                Yes, delete.
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Duplicate Confirmation Modal */}
      <Dialog open={showDuplicateModal} onOpenChange={setShowDuplicateModal}>
        <DialogContent className="sm:max-w-[500px] bg-white">
          <div className="text-center space-y-4 py-6">
            <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground">
              Are you sure you want to duplicate {getSelectedCount()} selected work areas?
            </h3>
            <p className="text-muted-foreground">You will not be able to undo this action.</p>
            <div className="flex gap-3 mt-6">
              <Button 
                variant="outline" 
                onClick={() => setShowDuplicateModal(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button 
                onClick={handleConfirmDuplicate}
                className="flex-1 btn-primary"
              >
                Yes, duplicate.
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

    </div>
  );
};

export default CreateNewProject;