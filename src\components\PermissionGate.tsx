import { ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { usePermissions } from '@/hooks/usePermissions';
import { UserType } from '@/types';

interface PermissionGateProps {
  children: ReactNode;
  requiredRoles?: UserType[];
  requiredPermissions?: string[];
  requireAllPermissions?: boolean; // true = AND logic, false = OR logic
  fallback?: ReactNode;
  requireAuth?: boolean;
}

const PermissionGate = ({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  requireAllPermissions = true,
  fallback = null,
  requireAuth = true,
}: PermissionGateProps) => {
  const { isAuthenticated, user } = useAuth();
  const { hasAllPermissions, hasAnyPermission } = usePermissions();

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    return <>{fallback}</>;
  }

  // Check role requirements
  if (requiredRoles.length > 0 && user) {
    const hasRequiredRole = requiredRoles.includes(user.user_type);
    if (!hasRequiredRole) {
      return <>{fallback}</>;
    }
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAllPermissions
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions);

    if (!hasRequiredPermissions) {
      return <>{fallback}</>;
    }
  }

  // All checks passed, render the content
  return <>{children}</>;
};

export default PermissionGate;
