import { useSelector, useDispatch } from 'react-redux';
import { useCallback, useEffect } from 'react';
import { RootState, AppDispatch } from '@/store';
import { loginUser, logoutUser, refreshToken, initializeAuth, clearError } from '@/store/slices/authSlice';
import { UserType } from '@/types';

export const useAuth = () => {
  const dispatch = useDispatch<AppDispatch>();
  const auth = useSelector((state: RootState) => state.auth);

  // Initialize auth on hook mount
  useEffect(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  // Auto-refresh token before expiry
  useEffect(() => {
    if (auth.isAuthenticated && auth.sessionExpiry) {
      const timeUntilExpiry = auth.sessionExpiry - Date.now();
      const refreshTime = timeUntilExpiry - (5 * 60 * 1000); // Refresh 5 minutes before expiry

      if (refreshTime > 0) {
        const timer = setTimeout(() => {
          dispatch(refreshToken());
        }, refreshTime);

        return () => clearTimeout(timer);
      }
    }
  }, [auth.isAuthenticated, auth.sessionExpiry, dispatch]);

  const login = useCallback(async (email: string, password: string) => {
    const result = await dispatch(loginUser({ email, password }));
    return result;
  }, [dispatch]);

  const logout = useCallback(async () => {
    const result = await dispatch(logoutUser());
    return result;
  }, [dispatch]);

  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const hasRole = useCallback((role: UserType | UserType[]) => {
    if (!auth.user) return false;
    
    if (Array.isArray(role)) {
      return role.includes(auth.user.user_type);
    }
    
    return auth.user.user_type === role;
  }, [auth.user]);

  const isSuperAdmin = useCallback(() => {
    return hasRole('super_admin');
  }, [hasRole]);

  const isProjectAdmin = useCallback(() => {
    return hasRole('project_admin');
  }, [hasRole]);

  const isServiceProvider = useCallback(() => {
    return hasRole('service_provider');
  }, [hasRole]);

  const isInspector = useCallback(() => {
    return hasRole('inspector');
  }, [hasRole]);

  const canAccessAdmin = useCallback(() => {
    return hasRole(['super_admin', 'project_admin']);
  }, [hasRole]);

  return {
    // State
    user: auth.user,
    token: auth.token,
    isAuthenticated: auth.isAuthenticated,
    loading: auth.loading,
    error: auth.error,
    sessionExpiry: auth.sessionExpiry,

    // Actions
    login,
    logout,
    clearAuthError,

    // Role checks
    hasRole,
    isSuperAdmin,
    isProjectAdmin,
    isServiceProvider,
    isInspector,
    canAccessAdmin,
  };
};
