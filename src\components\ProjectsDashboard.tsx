import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus } from "lucide-react";
import ProjectCard from "@/components/ProjectCard";
import { useAppDispatch } from "@/hooks/useAppDispatch";
import { useAppSelector } from "@/hooks/useAppSelector";
import { fetchProjects, updateProject } from "@/store/slices/projectsSlice";
import { loadDrafts } from "@/store/slices/workAreasSlice";
import { ProjectStatus } from "@/types";

// Map our internal status to the database status
const mapStatusToDb = (status: "Draft" | "Published" | "Completed"): ProjectStatus => {
  switch (status) {
    case "Draft":
      return "draft";
    case "Published":
      return "published";
    case "Completed":
      return "completed";
    default:
      return "draft";
  }
};

const mapDbStatusToDisplay = (status: ProjectStatus): "Draft" | "Published" | "Completed" => {
  switch (status) {
    case "draft":
      return "Draft";
    case "published":
      return "Published";
    case "completed":
      return "Completed";
    default:
      return "Draft";
  }
};

const ProjectsDashboard = () => {
  const dispatch = useAppDispatch();
  const { projects, loading, error } = useAppSelector((state) => state.projects);
  const [activeTab, setActiveTab] = useState<"Active" | "Archived">("Active");
  const [searchQuery, setSearchQuery] = useState("");

  // Load projects and drafts on component mount
  useEffect(() => {
    dispatch(fetchProjects());
    dispatch(loadDrafts());
  }, [dispatch]);

  // Filter projects based on search and active tab
  const filteredProjects = (projects || []).filter(project => {
    const matchesSearch = project.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.address?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesTab = activeTab === "Active"
      ? project.status !== "archived"
      : project.status === "archived";

    return matchesSearch && matchesTab;
  });

  const handleStatusChange = (projectId: string, newStatus: "Draft" | "Published" | "Completed") => {
    const dbStatus = mapStatusToDb(newStatus);
    dispatch(updateProject({ 
      id: parseInt(projectId), 
      projectData: { status: dbStatus } 
    }));
  };

  const handleManageProject = (projectId: string) => {
    // Navigate directly to overlay editor (single overlay per level)
    window.location.href = `/projects/${projectId}/work-areas/create`;
  };

  const handleCreateNew = () => {
    window.location.href = "/projects/create";
  };

  // Transform projects for the UI
  const transformedProjects = filteredProjects.map(project => ({
    id: project.id?.toString() || '',
    name: project.name || '',
    address: project.address || '',
    lastUpdated: project.lastUpdated || project.start_date || '',
    status: mapDbStatusToDisplay(project.status),
    image: project.image || ''
  }));

  return (
    <div className="flex-1 p-8">

      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-foreground">Projects</h1>
        <Button onClick={handleCreateNew} className="btn-primary flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Create New
        </Button>
      </div>

      {/* Tabs */}
      <div className="tab-nav mb-6">
        <button
          onClick={() => setActiveTab("Active")}
          className={`tab-item ${activeTab === "Active" ? "active" : ""}`}
        >
          Active
        </button>
        <button
          onClick={() => setActiveTab("Archived")}
          className={`tab-item ${activeTab === "Archived" ? "active" : ""}`}
        >
          Archived
        </button>
      </div>

      {/* Search */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search for a project..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="search-input pl-10"
        />
      </div>

      {/* Projects List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">Loading projects...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-destructive">Error: {error}</p>
          </div>
        ) : transformedProjects.length > 0 ? (
          transformedProjects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              onStatusChange={handleStatusChange}
              onManage={handleManageProject}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No projects found matching your search.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectsDashboard;