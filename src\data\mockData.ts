import {
  User, Project, ProjectMember, ProjectLevel, WorkArea, PlanOverlay,
  FirePenetration, FireStoppingSystem, Manufacturer, Notification
} from '@/types';

// Mock Users Data
export const mockUsers: User[] = [
  {
    id: 1,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    email_verified_at: "2024-01-15T10:00:00Z",
    password: "hashed_password",
    company_name: "ConnectaBuild",
    phone: "******-0101",
    user_type: "super_admin",
    discipline: "electrical",
    status: "active",
    date_joined: "2024-01-01T00:00:00Z",
    last_login: "2024-07-14T08:30:00Z"
  },
  {
    id: 2,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    password: "hashed_password",
    company_name: "BuildTech Solutions",
    phone: "******-0102",
    user_type: "project_admin",
    discipline: "plumbing",
    status: "active",
    date_joined: "2024-02-01T00:00:00Z",
    last_login: "2024-07-14T09:15:00Z"
  },
  {
    id: 3,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    password: "hashed_password",
    company_name: "FireTech Services",
    phone: "******-0103",
    user_type: "service_provider",
    discipline: "dry_fire_services",
    status: "active",
    date_joined: "2024-02-15T00:00:00Z",
    last_login: "2024-07-13T16:45:00Z"
  },
  {
    id: 4,
    first_name: "Emma",
    last_name: "Davis",
    email: "<EMAIL>",
    password: "hashed_password",
    company_name: "Quality Inspections",
    phone: "******-0104",
    user_type: "inspector",
    discipline: "electrical",
    status: "active",
    date_joined: "2024-03-01T00:00:00Z",
    last_login: "2024-07-14T07:20:00Z"
  },
  {
    id: 5,
    first_name: "David",
    last_name: "Brown",
    email: "<EMAIL>",
    password: "hashed_password",
    company_name: "HVAC Professionals",
    phone: "******-0105",
    user_type: "service_provider",
    discipline: "hvac",
    status: "active",
    date_joined: "2024-03-15T00:00:00Z",
    last_login: "2024-07-12T14:30:00Z"
  }
];

// Mock Projects Data
export const mockProjects: Project[] = [
  {
    id: 1,
    project_code: "PROJ-2024-001",
    name: "Sydney CBD Tower",
    address: "123 George Street, Sydney NSW 2000",
    project_admin_id: 2,
    client_company: "Urban Development Corp",
    status: "published",
    start_date: "2024-01-15",
    end_date: "2024-12-31",
    image: "/public/lovable-uploads/43668952-d2e0-4dc7-a431-1d07e6e2be82.png",
    lastUpdated: "2024-07-14"
  },
  {
    id: 2,
    project_code: "PROJ-2024-002",
    name: "Melbourne Residential Complex",
    address: "456 Collins Street, Melbourne VIC 3000",
    project_admin_id: 2,
    client_company: "Metro Living",
    status: "published",
    start_date: "2024-03-01",
    end_date: "2025-02-28",
    lastUpdated: "2024-07-13"
  },
  {
    id: 3,
    project_code: "PROJ-2024-003",
    name: "Brisbane Office Building",
    address: "789 Queen Street, Brisbane QLD 4000",
    project_admin_id: 2,
    client_company: "Corporate Spaces",
    status: "draft",
    start_date: "2024-05-01",
    end_date: "2025-04-30",
    lastUpdated: "2024-07-12"
  },
  {
    id: 4,
    project_code: "PROJ-2024-004",
    name: "Perth Medical Center",
    address: "321 Hay Street, Perth WA 6000",
    project_admin_id: 2,
    client_company: "HealthCare Properties",
    status: "completed",
    start_date: "2023-06-01",
    end_date: "2024-05-31",
    lastUpdated: "2024-06-01"
  }
];

// Mock Project Members
export const mockProjectMembers: ProjectMember[] = [
  {
    id: 1,
    project_id: 1,
    user_id: 2,
    role: "project_admin",
    discipline: "plumbing",
    invited_by: 1,
    status: "active",
    permissions: { read: true, write: true, admin: true }
  },
  {
    id: 2,
    project_id: 1,
    user_id: 3,
    role: "service_provider",
    discipline: "dry_fire_services",
    invited_by: 2,
    status: "active",
    permissions: { read: true, write: true, admin: false }
  },
  {
    id: 3,
    project_id: 1,
    user_id: 4,
    role: "inspector",
    discipline: "electrical",
    invited_by: 2,
    status: "active",
    permissions: { read: true, write: false, admin: false }
  }
];

// Mock Project Levels
export const mockProjectLevels: ProjectLevel[] = [
  {
    id: 1,
    project_id: 1,
    level_name: "Basement 1",
    level_number: -1,
    level_type: "basement"
  },
  {
    id: 2,
    project_id: 1,
    level_name: "Ground Floor",
    level_number: 0,
    level_type: "ground"
  },
  {
    id: 3,
    project_id: 1,
    level_name: "Level 1",
    level_number: 1,
    level_type: "floor"
  },
  {
    id: 4,
    project_id: 1,
    level_name: "Level 2",
    level_number: 2,
    level_type: "floor"
  },
  {
    id: 5,
    project_id: 1,
    level_name: "Roof",
    level_number: 3,
    level_type: "roof"
  }
];

// Mock Work Areas
export const mockWorkAreas: WorkArea[] = [
  {
    id: 1,
    project_id: 1,
    level_id: 2,
    area_code: "A-101",
    area_name: "Apartment 101",
    area_type: "floor",
    category: "apartment",
    frl_rating: "FRL-120/120/120",
    coordinates: [
      { x: 100, y: 100 },
      { x: 300, y: 100 },
      { x: 300, y: 250 },
      { x: 100, y: 250 }
    ],
    color: "#FF6B6B",
    pending: false
  },
  {
    id: 2,
    project_id: 1,
    level_id: 2,
    area_code: "A-102",
    area_name: "Apartment 102",
    area_type: "floor",
    category: "apartment",
    frl_rating: "FRL-120/120/120",
    coordinates: [
      { x: 320, y: 100 },
      { x: 520, y: 100 },
      { x: 520, y: 250 },
      { x: 320, y: 250 }
    ],
    color: "#4ECDC4",
    pending: false
  },
  {
    id: 3,
    project_id: 1,
    level_id: 2,
    area_code: "C-001",
    area_name: "Lobby",
    area_type: "floor",
    category: "common_area",
    frl_rating: "FRL-90/90/90",
    coordinates: [
      { x: 100, y: 270 },
      { x: 520, y: 270 },
      { x: 520, y: 350 },
      { x: 100, y: 350 }
    ],
    color: "#45B7D1",
    pending: false
  },
  {
    id: 4,
    project_id: 1,
    level_id: 2,
    area_code: "M-001",
    area_name: "Electrical Room",
    area_type: "floor",
    category: "mechanical",
    frl_rating: "FRL-180/180/180",
    coordinates: [
      { x: 540, y: 100 },
      { x: 600, y: 100 },
      { x: 600, y: 200 },
      { x: 540, y: 200 }
    ],
    color: "#F7DC6F",
    pending: false
  }
];

// Mock Plan Overlays
export const mockPlanOverlays: PlanOverlay[] = [
  {
    id: 1,
    project_id: 1,
    level_id: 2,
    overlay_type: "architectural",
    file_path: "/public/lovable-uploads/43668952-d2e0-4dc7-a431-1d07e6e2be82.png",
    is_base_plan: true,
    uploaded_by: 2
  }
];

// Mock Manufacturers
export const mockManufacturers: Manufacturer[] = [
  {
    id: 1,
    name: "Hilti Corporation",
    contact_person: "John Williams",
    phone: "******-HILTI-US",
    email: "<EMAIL>"
  },
  {
    id: 2,
    name: "3M Fire Protection",
    contact_person: "Sarah Miller",
    phone: "******-3M-HELPS",
    email: "<EMAIL>"
  }
];

// Mock Fire Stopping Systems
export const mockFireStoppingSystems: FireStoppingSystem[] = [
  {
    id: 1,
    matrix_id: "HILTI-CP-617",
    manufacturer_id: 1,
    system_name: "CP 617 Firestop Sealant",
    service_types: ["electrical", "plumbing"],
    frl_rating: "FRL-120/120/120"
  },
  {
    id: 2,
    matrix_id: "3M-FB-3000WT",
    manufacturer_id: 2,
    system_name: "FB-3000WT Fire Barrier",
    service_types: ["hvac", "wet_fire_services"],
    frl_rating: "FRL-180/180/180"
  }
];

// Mock Fire Penetrations
export const mockFirePenetrations: FirePenetration[] = [
  {
    id: 1,
    project_id: 1,
    service_id: "ELEC-001",
    work_area_id: 1,
    service_provider_id: 3,
    inspector_id: 4,
    service_type: "electrical",
    fire_stopping_system_id: 1,
    frl_rating: "FRL-120/120/120",
    status: "ready_for_inspection",
    coordinates: { x: 200, y: 150 }
  },
  {
    id: 2,
    project_id: 1,
    service_id: "PLUMB-001",
    work_area_id: 2,
    service_provider_id: 5,
    inspector_id: 4,
    service_type: "plumbing",
    fire_stopping_system_id: 1,
    frl_rating: "FRL-120/120/120",
    status: "images_uploaded",
    coordinates: { x: 420, y: 180 }
  }
];

// Mock Notifications
export const mockNotifications: Notification[] = [
  {
    id: 1,
    user_id: 2,
    type: "penetration_update",
    title: "Penetration Ready for Inspection",
    message: "Fire penetration ELEC-001 in Sydney CBD Tower is ready for inspection.",
    created_at: "2024-07-14T09:00:00Z"
  },
  {
    id: 2,
    user_id: 4,
    type: "new_assignment",
    title: "New Inspection Assignment",
    message: "You have been assigned to inspect penetration ELEC-001.",
    created_at: "2024-07-14T08:30:00Z"
  }
];