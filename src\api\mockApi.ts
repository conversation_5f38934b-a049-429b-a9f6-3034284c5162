import {
  mockUsers, mockProjects, mockProjectMembers, mockProjectLevels,
  mockWorkAreas, mockPlanOverlays, mockFirePenetrations,
  mockFireStoppingSystems, mockManufacturers, mockNotifications
} from '@/data/mockData';
import {
  User, Project, ProjectMember, ProjectLevel, WorkArea, PlanOverlay,
  FirePenetration, FireStoppingSystem, Manufacturer, Notification,
  ApiResponse, PaginatedResponse
} from '@/types';

// Simulate API delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// Storage keys
const STORAGE_KEYS = {
  USERS: 'connecta_users',
  PROJECTS: 'connecta_projects',
  PROJECT_MEMBERS: 'connecta_project_members',
  PROJECT_LEVELS: 'connecta_project_levels',
  WORK_AREAS: 'connecta_work_areas',
  WORK_AREA_DRAFTS: 'connecta_work_area_drafts',
  PLAN_OVERLAYS: 'connecta_plan_overlays',
  FIRE_PENETRATIONS: 'connecta_fire_penetrations',
  FIRE_STOPPING_SYSTEMS: 'connecta_fire_stopping_systems',
  MANUFACTURERS: 'connecta_manufacturers',
  NOTIFICATIONS: 'connecta_notifications'
};

// Storage utilities
const saveToStorage = (key: string, data: any) => {
  localStorage.setItem(key, JSON.stringify(data));
};

const loadFromStorage = <T>(key: string, defaultData: T[]): T[] => {
  const stored = localStorage.getItem(key);
  return stored ? JSON.parse(stored) : defaultData;
};

// Initialize data from storage or use defaults
let users = loadFromStorage(STORAGE_KEYS.USERS, mockUsers);
let projects = loadFromStorage(STORAGE_KEYS.PROJECTS, mockProjects);
let projectMembers = loadFromStorage(STORAGE_KEYS.PROJECT_MEMBERS, mockProjectMembers);
let projectLevels = loadFromStorage(STORAGE_KEYS.PROJECT_LEVELS, mockProjectLevels);
let workAreas = loadFromStorage(STORAGE_KEYS.WORK_AREAS, mockWorkAreas);

let planOverlays = loadFromStorage(STORAGE_KEYS.PLAN_OVERLAYS, mockPlanOverlays);
let firePenetrations = loadFromStorage(STORAGE_KEYS.FIRE_PENETRATIONS, mockFirePenetrations);
let fireStoppingSystems = loadFromStorage(STORAGE_KEYS.FIRE_STOPPING_SYSTEMS, mockFireStoppingSystems);
let manufacturers = loadFromStorage(STORAGE_KEYS.MANUFACTURERS, mockManufacturers);
let notifications = loadFromStorage(STORAGE_KEYS.NOTIFICATIONS, mockNotifications);

// Reset function to restore original data
export const resetDatabase = () => {
  users.length = 0;
  users.push(...mockUsers);
  saveToStorage(STORAGE_KEYS.USERS, users);

  projects.length = 0;
  projects.push(...mockProjects);
  saveToStorage(STORAGE_KEYS.PROJECTS, projects);

  projectMembers.length = 0;
  projectMembers.push(...mockProjectMembers);
  saveToStorage(STORAGE_KEYS.PROJECT_MEMBERS, projectMembers);

  projectLevels.length = 0;
  projectLevels.push(...mockProjectLevels);
  saveToStorage(STORAGE_KEYS.PROJECT_LEVELS, projectLevels);

  workAreas.length = 0;
  workAreas.push(...mockWorkAreas);
  saveToStorage(STORAGE_KEYS.WORK_AREAS, workAreas);


  planOverlays.length = 0;
  planOverlays.push(...mockPlanOverlays);
  saveToStorage(STORAGE_KEYS.PLAN_OVERLAYS, planOverlays);

  firePenetrations.length = 0;
  firePenetrations.push(...mockFirePenetrations);
  saveToStorage(STORAGE_KEYS.FIRE_PENETRATIONS, firePenetrations);

  fireStoppingSystems.length = 0;
  fireStoppingSystems.push(...mockFireStoppingSystems);
  saveToStorage(STORAGE_KEYS.FIRE_STOPPING_SYSTEMS, fireStoppingSystems);

  manufacturers.length = 0;
  manufacturers.push(...mockManufacturers);
  saveToStorage(STORAGE_KEYS.MANUFACTURERS, manufacturers);

  notifications.length = 0;
  notifications.push(...mockNotifications);
  saveToStorage(STORAGE_KEYS.NOTIFICATIONS, notifications);
};

// Users API
export const usersApi = {
  getAll: async (): Promise<ApiResponse<User[]>> => {
    await delay();
    return { data: users, success: true };
  },

  getById: async (id: number): Promise<ApiResponse<User>> => {
    await delay();
    const user = users.find(u => u.id === id);
    if (!user) throw new Error('User not found');
    return { data: user, success: true };
  },

  create: async (userData: Omit<User, 'id'>): Promise<ApiResponse<User>> => {
    await delay();
    const newUser = { ...userData, id: Math.max(...users.map(u => u.id)) + 1 };
    users.push(newUser);
    saveToStorage(STORAGE_KEYS.USERS, users);
    return { data: newUser, success: true };
  },

  update: async (id: number, userData: Partial<User>): Promise<ApiResponse<User>> => {
    await delay();
    const index = users.findIndex(u => u.id === id);
    if (index === -1) throw new Error('User not found');
    users[index] = { ...users[index], ...userData };
    saveToStorage(STORAGE_KEYS.USERS, users);
    return { data: users[index], success: true };
  },

  delete: async (id: number): Promise<ApiResponse<boolean>> => {
    await delay();
    const index = users.findIndex(u => u.id === id);
    if (index === -1) throw new Error('User not found');
    users.splice(index, 1);
    saveToStorage(STORAGE_KEYS.USERS, users);
    return { data: true, success: true };
  }
};

// Projects API
export const projectsApi = {
  getAll: async (): Promise<ApiResponse<Project[]>> => {
    await delay();
    return { data: projects, success: true };
  },

  getById: async (id: number): Promise<ApiResponse<Project>> => {
    await delay();
    const project = projects.find(p => p.id === id);
    if (!project) throw new Error('Project not found');
    return { data: project, success: true };
  },

  create: async (projectData: Omit<Project, 'id'>): Promise<ApiResponse<Project>> => {
    await delay();
    const newProject = { 
      ...projectData, 
      id: Math.max(...projects.map(p => p.id)) + 1,
      lastUpdated: new Date().toISOString().split('T')[0]
    };
    projects.push(newProject);
    saveToStorage(STORAGE_KEYS.PROJECTS, projects);
    return { data: newProject, success: true };
  },

  update: async (id: number, projectData: Partial<Project>): Promise<ApiResponse<Project>> => {
    await delay();
    const index = projects.findIndex(p => p.id === id);
    if (index === -1) throw new Error('Project not found');
    projects[index] = { 
      ...projects[index], 
      ...projectData,
      lastUpdated: new Date().toISOString().split('T')[0]
    };
    saveToStorage(STORAGE_KEYS.PROJECTS, projects);
    return { data: projects[index], success: true };
  },

  delete: async (id: number): Promise<ApiResponse<boolean>> => {
    await delay();
    const index = projects.findIndex(p => p.id === id);
    if (index === -1) throw new Error('Project not found');
    projects.splice(index, 1);
    saveToStorage(STORAGE_KEYS.PROJECTS, projects);
    return { data: true, success: true };
  }
};

// Project Members API
export const projectMembersApi = {
  getByProjectId: async (projectId: number): Promise<ApiResponse<ProjectMember[]>> => {
    await delay();
    const members = projectMembers.filter(m => m.project_id === projectId);
    return { data: members, success: true };
  },

  create: async (memberData: Omit<ProjectMember, 'id'>): Promise<ApiResponse<ProjectMember>> => {
    await delay();
    const newMember = { ...memberData, id: Math.max(...projectMembers.map(m => m.id)) + 1 };
    projectMembers.push(newMember);
    saveToStorage(STORAGE_KEYS.PROJECT_MEMBERS, projectMembers);
    return { data: newMember, success: true };
  }
};

// Project Levels API
export const projectLevelsApi = {
  getByProjectId: async (projectId: number): Promise<ApiResponse<ProjectLevel[]>> => {
    await delay();
    const levels = projectLevels.filter(l => l.project_id === projectId);
    return { data: levels, success: true };
  },

  create: async (levelData: Omit<ProjectLevel, 'id'>): Promise<ApiResponse<ProjectLevel>> => {
    await delay();
    const newLevel = { ...levelData, id: Math.max(...projectLevels.map(l => l.id)) + 1 };
    projectLevels.push(newLevel);
    saveToStorage(STORAGE_KEYS.PROJECT_LEVELS, projectLevels);
    return { data: newLevel, success: true };
  }
};

// Work Areas API
export const workAreasApi = {
  getByProjectId: async (projectId: number): Promise<ApiResponse<WorkArea[]>> => {
    await delay();
    const areas = workAreas.filter(w => w.project_id === projectId);
    return { data: areas, success: true };
  },

  getByLevelId: async (levelId: number): Promise<ApiResponse<WorkArea[]>> => {
    await delay();
    const areas = workAreas.filter(w => w.level_id === levelId);
    return { data: areas, success: true };
  },

  create: async (areaData: Omit<WorkArea, 'id'>): Promise<ApiResponse<WorkArea>> => {
    await delay();
    const newArea = { ...areaData, id: Math.max(...workAreas.map(w => typeof w.id === 'number' ? w.id : 0)) + 1 };
    workAreas.push(newArea);
    saveToStorage(STORAGE_KEYS.WORK_AREAS, workAreas);
    return { data: newArea, success: true };
  },

  update: async (id: number, areaData: Partial<WorkArea>): Promise<ApiResponse<WorkArea>> => {
    await delay();
    const index = workAreas.findIndex(w => w.id === id);
    if (index === -1) throw new Error('Work area not found');
    workAreas[index] = { ...workAreas[index], ...areaData };
    saveToStorage(STORAGE_KEYS.WORK_AREAS, workAreas);
    return { data: workAreas[index], success: true };
  },

  delete: async (id: number): Promise<ApiResponse<boolean>> => {
    await delay();
    const index = workAreas.findIndex(w => w.id === id);
    if (index === -1) throw new Error('Work area not found');
    workAreas.splice(index, 1);
    saveToStorage(STORAGE_KEYS.WORK_AREAS, workAreas);
    return { data: true, success: true };
  }
};

// Plan Overlays API
export const planOverlaysApi = {
  getByLevelId: async (levelId: number): Promise<ApiResponse<PlanOverlay[]>> => {
    await delay();
    const overlays = planOverlays.filter(o => o.level_id === levelId);
    return { data: overlays, success: true };
  },

  create: async (overlayData: Omit<PlanOverlay, 'id'>): Promise<ApiResponse<PlanOverlay>> => {
    await delay();
    const newOverlay = { ...overlayData, id: Math.max(...planOverlays.map(o => o.id)) + 1 };
    planOverlays.push(newOverlay);
    saveToStorage(STORAGE_KEYS.PLAN_OVERLAYS, planOverlays);
    return { data: newOverlay, success: true };
  }
};

// Fire Penetrations API
export const firePenetrationsApi = {
  getByProjectId: async (projectId: number): Promise<ApiResponse<FirePenetration[]>> => {
    await delay();
    const penetrations = firePenetrations.filter(p => p.project_id === projectId);
    return { data: penetrations, success: true };
  },

  getByWorkAreaId: async (workAreaId: number): Promise<ApiResponse<FirePenetration[]>> => {
    await delay();
    const penetrations = firePenetrations.filter(p => p.work_area_id === workAreaId);
    return { data: penetrations, success: true };
  },

  create: async (penetrationData: Omit<FirePenetration, 'id'>): Promise<ApiResponse<FirePenetration>> => {
    await delay();
    const newPenetration = { ...penetrationData, id: Math.max(...firePenetrations.map(p => p.id)) + 1 };
    firePenetrations.push(newPenetration);
    saveToStorage(STORAGE_KEYS.FIRE_PENETRATIONS, firePenetrations);
    return { data: newPenetration, success: true };
  },

  update: async (id: number, penetrationData: Partial<FirePenetration>): Promise<ApiResponse<FirePenetration>> => {
    await delay();
    const index = firePenetrations.findIndex(p => p.id === id);
    if (index === -1) throw new Error('Fire penetration not found');
    firePenetrations[index] = { ...firePenetrations[index], ...penetrationData };
    saveToStorage(STORAGE_KEYS.FIRE_PENETRATIONS, firePenetrations);
    return { data: firePenetrations[index], success: true };
  }
};

// Fire Stopping Systems API
export const fireStoppingSystemsApi = {
  getAll: async (): Promise<ApiResponse<FireStoppingSystem[]>> => {
    await delay();
    return { data: fireStoppingSystems, success: true };
  }
};

// Work Area Drafts API

// Manufacturers API
export const manufacturersApi = {
  getAll: async (): Promise<ApiResponse<Manufacturer[]>> => {
    await delay();
    return { data: manufacturers, success: true };
  }
};

// Notifications API
export const notificationsApi = {
  getByUserId: async (userId: number): Promise<ApiResponse<Notification[]>> => {
    await delay();
    const userNotifications = notifications.filter(n => n.user_id === userId);
    return { data: userNotifications, success: true };
  },

  markAsRead: async (id: number): Promise<ApiResponse<boolean>> => {
    await delay();
    const index = notifications.findIndex(n => n.id === id);
    if (index !== -1) {
      notifications[index].read_at = new Date().toISOString();
      saveToStorage(STORAGE_KEYS.NOTIFICATIONS, notifications);
    }
    return { data: true, success: true };
  }
};