import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { usePermissions } from "@/hooks/usePermissions";
import { fetchProjectById } from "@/store/slices/projectsSlice";
import DashboardLayout from "@/components/DashboardLayout";
import ProjectTeamManagement from "@/components/ProjectTeamManagement";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  ArrowLeft, 
  Upload, 
  MapPin, 
  Calendar, 
  Building, 
  Users, 
  FileText, 
  Layers,
  AlertTriangle 
} from "lucide-react";
import { ProjectMember, Project } from "@/types";
import { toast } from "sonner";
import PermissionGate from "@/components/PermissionGate";

const ProjectSetup = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { currentProject, loading } = useAppSelector((state) => state.projects);
  const { canUpdateProjects, canManageProjectTeam } = usePermissions();

  const [activeTab, setActiveTab] = useState("details");
  const [projectMembers, setProjectMembers] = useState<ProjectMember[]>([]);
  const [projectForm, setProjectForm] = useState({
    name: "",
    address: "",
    client_company: "",
    start_date: "",
    end_date: "",
    description: "",
  });

  useEffect(() => {
    if (projectId) {
      dispatch(fetchProjectById(parseInt(projectId)));
    }
  }, [dispatch, projectId]);

  useEffect(() => {
    if (currentProject) {
      setProjectForm({
        name: currentProject.name,
        address: currentProject.address,
        client_company: currentProject.client_company,
        start_date: currentProject.start_date,
        end_date: currentProject.end_date || "",
        description: "", // Add description field to Project type if needed
      });
    }
  }, [currentProject]);

  const handleAddMember = (member: Omit<ProjectMember, 'id'>) => {
    const newMember: ProjectMember = {
      ...member,
      id: Math.max(...projectMembers.map(m => m.id), 0) + 1,
    };
    setProjectMembers([...projectMembers, newMember]);
  };

  const handleRemoveMember = (memberId: number) => {
    setProjectMembers(projectMembers.filter(m => m.id !== memberId));
  };

  const handleUpdateMember = (memberId: number, updates: Partial<ProjectMember>) => {
    setProjectMembers(projectMembers.map(m => 
      m.id === memberId ? { ...m, ...updates } : m
    ));
  };

  const handleSaveProject = () => {
    // Implementation for saving project details
    toast.success("Project details saved successfully");
  };

  const handleUploadPlans = () => {
    // Implementation for plan upload
    toast.success("Plans uploaded successfully");
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-connecta-cyan"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!currentProject) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh] p-4">
          <Alert className="border-destructive max-w-md">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-center">
              Project not found or you don't have access to it.
            </AlertDescription>
          </Alert>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button 
            variant="outline" 
            onClick={() => navigate("/projects")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Projects
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold">{currentProject.name}</h1>
            <p className="text-muted-foreground">Project Setup & Configuration</p>
          </div>
        </div>

        {/* Project Status */}
        <div className="mb-6">
          <Badge 
            className={
              currentProject.status === 'published' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }
          >
            {currentProject.status}
          </Badge>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="details" className="flex items-center gap-2">
              <Building className="w-4 h-4" />
              Project Details
            </TabsTrigger>
            <TabsTrigger value="team" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Team Management
            </TabsTrigger>
            <TabsTrigger value="plans" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Plans & Overlays
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Layers className="w-4 h-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Project Details Tab */}
          <TabsContent value="details">
            <Card>
              <CardHeader>
                <CardTitle>Project Information</CardTitle>
                <CardDescription>
                  Configure basic project details and metadata
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="project_name">Project Name</Label>
                    <Input
                      id="project_name"
                      value={projectForm.name}
                      onChange={(e) => setProjectForm({ ...projectForm, name: e.target.value })}
                      disabled={!canUpdateProjects}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="client_company">Client Company</Label>
                    <Input
                      id="client_company"
                      value={projectForm.client_company}
                      onChange={(e) => setProjectForm({ ...projectForm, client_company: e.target.value })}
                      disabled={!canUpdateProjects}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="address">Project Address</Label>
                  <div className="flex gap-2">
                    <Input
                      id="address"
                      value={projectForm.address}
                      onChange={(e) => setProjectForm({ ...projectForm, address: e.target.value })}
                      placeholder="Enter project address"
                      disabled={!canUpdateProjects}
                    />
                    <Button variant="outline" className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Validate
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="start_date">Start Date</Label>
                    <Input
                      id="start_date"
                      type="date"
                      value={projectForm.start_date}
                      onChange={(e) => setProjectForm({ ...projectForm, start_date: e.target.value })}
                      disabled={!canUpdateProjects}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="end_date">End Date</Label>
                    <Input
                      id="end_date"
                      type="date"
                      value={projectForm.end_date}
                      onChange={(e) => setProjectForm({ ...projectForm, end_date: e.target.value })}
                      disabled={!canUpdateProjects}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Project Description</Label>
                  <Textarea
                    id="description"
                    value={projectForm.description}
                    onChange={(e) => setProjectForm({ ...projectForm, description: e.target.value })}
                    placeholder="Enter project description..."
                    rows={4}
                    disabled={!canUpdateProjects}
                  />
                </div>

                <PermissionGate requiredPermissions={['projects.update']}>
                  <div className="flex justify-end">
                    <Button onClick={handleSaveProject}>
                      Save Changes
                    </Button>
                  </div>
                </PermissionGate>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Team Management Tab */}
          <TabsContent value="team">
            <PermissionGate 
              requiredPermissions={['projects.read']}
              fallback={
                <Alert className="border-destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    You don't have permission to view team management.
                  </AlertDescription>
                </Alert>
              }
            >
              <ProjectTeamManagement
                projectId={currentProject.id}
                projectMembers={projectMembers}
                onAddMember={handleAddMember}
                onRemoveMember={handleRemoveMember}
                onUpdateMember={handleUpdateMember}
              />
            </PermissionGate>
          </TabsContent>

          {/* Plans & Overlays Tab */}
          <TabsContent value="plans">
            <Card>
              <CardHeader>
                <CardTitle>Floor Plans & Overlays</CardTitle>
                <CardDescription>
                  Upload and manage project floor plans and overlay systems
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <PermissionGate requiredPermissions={['plans.upload']}>
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                      <Upload className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">Upload Floor Plans</h3>
                      <p className="text-muted-foreground mb-4">
                        Drag and drop PDF files or click to browse
                      </p>
                      <Button onClick={handleUploadPlans}>
                        Choose Files
                      </Button>
                    </div>
                  </PermissionGate>

                  <div>
                    <h4 className="text-lg font-semibold mb-4">Overlay Configuration</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {[
                        'Architectural',
                        'Structural', 
                        'Electrical',
                        'Plumbing',
                        'HVAC',
                        'Dry Fire Services',
                        'Wet Fire Services'
                      ].map((overlay) => (
                        <Card key={overlay} className="p-4">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{overlay}</span>
                            <Badge variant="outline">Enabled</Badge>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Project Settings</CardTitle>
                <CardDescription>
                  Configure project-specific settings and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Project settings will be available in the next update.
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ProjectSetup;
