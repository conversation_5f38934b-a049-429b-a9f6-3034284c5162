import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User, UserType } from '@/types';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  sessionExpiry: number | null;
}

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('auth_token'),
  isAuthenticated: !!localStorage.getItem('auth_token'),
  loading: false,
  error: null,
  sessionExpiry: null,
};

// Mock authentication API calls
const mockAuthApi = {
  login: async (email: string, password: string) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock user data based on email
    let userType: UserType = 'inspector';
    let userData: Partial<User> = {
      email,
      first_name: 'User',
      last_name: 'Name',
      company_name: 'Default Company',
      phone: '******-0000',
      status: 'active',
      discipline: 'electrical',
    };

    if (email.includes('superadmin')) {
      userType = 'super_admin';
      userData = { ...userData, first_name: 'Super', last_name: 'Admin' };
    } else if (email.includes('projectadmin')) {
      userType = 'project_admin';
      userData = { ...userData, first_name: 'Project', last_name: 'Admin' };
    } else if (email.includes('serviceprovider')) {
      userType = 'service_provider';
      userData = { ...userData, first_name: 'Service', last_name: 'Provider' };
    } else if (email.includes('inspector')) {
      userType = 'inspector';
      userData = { ...userData, first_name: 'Inspector', last_name: 'User' };
    }

    const user: User = {
      id: Math.floor(Math.random() * 1000),
      ...userData,
      user_type: userType,
      date_joined: new Date().toISOString(),
      last_login: new Date().toISOString(),
      password: '', // Never return password
    } as User;

    const token = `mock_token_${Date.now()}_${user.id}`;
    
    return { user, token };
  },

  logout: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return true;
  },

  refreshToken: async (token: string) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { token: `refreshed_${token}`, expiresIn: 3600 };
  },

  forgotPassword: async (email: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { message: 'Password reset email sent' };
  },

  resetPassword: async (token: string, newPassword: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { message: 'Password reset successful' };
  },
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async ({ email, password }: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await mockAuthApi.login(email, password);
      
      // Store token in localStorage
      localStorage.setItem('auth_token', response.token);
      localStorage.setItem('user_data', JSON.stringify(response.user));
      
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Login failed');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      await mockAuthApi.logout();
      
      // Clear localStorage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      localStorage.removeItem('userRole');
      localStorage.removeItem('userEmail');
      
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Logout failed');
    }
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      if (!state.auth.token) {
        throw new Error('No token available');
      }
      
      const response = await mockAuthApi.refreshToken(state.auth.token);
      localStorage.setItem('auth_token', response.token);
      
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Token refresh failed');
    }
  }
);

export const forgotPassword = createAsyncThunk(
  'auth/forgotPassword',
  async (email: string, { rejectWithValue }) => {
    try {
      const response = await mockAuthApi.forgotPassword(email);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Password reset request failed');
    }
  }
);

export const resetPassword = createAsyncThunk(
  'auth/resetPassword',
  async ({ token, newPassword }: { token: string; newPassword: string }, { rejectWithValue }) => {
    try {
      const response = await mockAuthApi.resetPassword(token, newPassword);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Password reset failed');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSessionExpiry: (state, action: PayloadAction<number>) => {
      state.sessionExpiry = action.payload;
    },
    initializeAuth: (state) => {
      const token = localStorage.getItem('auth_token');
      const userData = localStorage.getItem('user_data');
      
      if (token && userData) {
        try {
          state.token = token;
          state.user = JSON.parse(userData);
          state.isAuthenticated = true;
        } catch (error) {
          // Clear invalid data
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_data');
          state.token = null;
          state.user = null;
          state.isAuthenticated = false;
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.sessionExpiry = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      })
      // Logout
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.sessionExpiry = null;
        state.error = null;
      })
      // Refresh token
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.token = action.payload.token;
        state.sessionExpiry = Date.now() + (action.payload.expiresIn * 1000);
      })
      // Forgot password
      .addCase(forgotPassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(forgotPassword.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Reset password
      .addCase(resetPassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resetPassword.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setSessionExpiry, initializeAuth } = authSlice.actions;
export default authSlice.reducer;
